function f(n){const t=new Set;return n&&n.length>0&&n.flatMap(e=>e.sections||[]).flatMap(e=>e.changes).forEach(e=>{t.add(e.path)}),t}function u(n,t,e={},s={}){const d=f(n),i=Array.from(d);i.length===0&&i.push(...t.map(a=>a.new_path||a.old_path).filter(Boolean));const h=i.every(a=>s[a]==="applied"),c=i.filter(a=>!s[a]||s[a]==="none"),l=[];return c.forEach(a=>{const p=n.flatMap(o=>o.sections||[]).flatMap(o=>o.changes).find(o=>o.path===a);if(p)l.push({path:a,originalCode:p.originalCode,newCode:e[a]||p.modifiedCode});else{const o=t.find(r=>(r.new_path||r.old_path)===a);o&&l.push({path:a,originalCode:o.old_contents||"",newCode:e[a]||o.new_contents||""})}}),{filesToApply:l,allPaths:i,areAllPathsApplied:h}}async function g(n,t){n.length&&t&&await Promise.all(n.map(async e=>await t(e.path,e.originalCode,e.newCode)))}function w(n){return n.sort((t,e)=>{const s=new Date(t.updated_at||t.started_at);return new Date(e.updated_at||e.started_at).getTime()-s.getTime()})}export{g as a,f as g,u as p,w as s};
