import{S as B,i as Q,s as W,N as U,f as p,t as R,u as d,v as S,w as f,k as m,n as T,a4 as F,a5 as D,a6 as I,E as g,F as h,a7 as _,G as y,V as N,Y as E,D as H,d as V,a8 as j,Z as Y}from"./SpinnerAugment-uKUHz-bK.js";import{e as q}from"./IconButtonAugment-CQzh_Hae.js";import{A as Z,D as x}from"./index-DP6mqmYw.js";import{B as k}from"./ButtonAugment-D5QDitBR.js";import{C as J}from"./chevron-down-BMBumfK8.js";import{T as K}from"./CardAugment-BqjOeIg4.js";import{R as A}from"./message-broker-DdVtH9Vr.js";function G(s,e,n){const t=s.slice();return t[17]=e[n],t}function O(s){let e,n,t,r;function o(l){s[12](l)}function i(l){s[13](l)}let c={$$slots:{default:[st]},$$scope:{ctx:s}};return s[1]!==void 0&&(c.requestClose=s[1]),s[0]!==void 0&&(c.focusedIndex=s[0]),e=new x.Root({props:c}),D.push(()=>I(e,"requestClose",o)),D.push(()=>I(e,"focusedIndex",i)),{c(){g(e.$$.fragment)},m(l,a){h(e,l,a),r=!0},p(l,a){const $={};1048596&a&&($.$$scope={dirty:a,ctx:l}),!n&&2&a&&(n=!0,$.requestClose=l[1],_(()=>n=!1)),!t&&1&a&&(t=!0,$.focusedIndex=l[0],_(()=>t=!1)),e.$set($)},i(l){r||(f(e.$$.fragment,l),r=!0)},o(l){d(e.$$.fragment,l),r=!1},d(l){y(e,l)}}}function P(s){let e,n;return e=new K({props:{content:"Workspace guidelines are always applied",$$slots:{default:[at]},$$scope:{ctx:s}}}),{c(){g(e.$$.fragment)},m(t,r){h(e,t,r),n=!0},p(t,r){const o={};1048576&r&&(o.$$scope={dirty:r,ctx:t}),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){y(e,t)}}}function X(s){let e,n=s[2].label+"";return{c(){e=E(n)},m(t,r){p(t,e,r)},p(t,r){4&r&&n!==(n=t[2].label+"")&&Y(e,n)},d(t){t&&m(e)}}}function tt(s){let e,n;return e=new J({props:{slot:"iconRight"}}),{c(){g(e.$$.fragment)},m(t,r){h(e,t,r),n=!0},p:T,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){y(e,t)}}}function et(s){let e,n,t;return n=new k({props:{color:"neutral",size:1,variant:"soft",$$slots:{iconRight:[tt],default:[X]},$$scope:{ctx:s}}}),{c(){e=H("div"),g(n.$$.fragment),V(e,"class","c-dropdown-label svelte-9n7h82")},m(r,o){p(r,e,o),h(n,e,null),t=!0},p(r,o){const i={};1048580&o&&(i.$$scope={dirty:o,ctx:r}),n.$set(i)},i(r){t||(f(n.$$.fragment,r),t=!0)},o(r){d(n.$$.fragment,r),t=!1},d(r){r&&m(e),y(n)}}}function nt(s){let e,n=s[17].label+"";return{c(){e=E(n)},m(t,r){p(t,e,r)},p:T,d(t){t&&m(e)}}}function L(s){let e,n;return e=new x.Item({props:{onSelect:function(){return s[11](s[17])},highlight:s[2].label===s[17].label,$$slots:{default:[nt]},$$scope:{ctx:s}}}),{c(){g(e.$$.fragment)},m(t,r){h(e,t,r),n=!0},p(t,r){s=t;const o={};4&r&&(o.highlight=s[2].label===s[17].label),1048576&r&&(o.$$scope={dirty:r,ctx:s}),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){y(e,t)}}}function M(s){let e,n,t,r;return e=new x.Separator({}),t=new x.Label({props:{$$slots:{default:[ot]},$$scope:{ctx:s}}}),{c(){g(e.$$.fragment),n=N(),g(t.$$.fragment)},m(o,i){h(e,o,i),p(o,n,i),h(t,o,i),r=!0},p(o,i){const c={};1048596&i&&(c.$$scope={dirty:i,ctx:o}),t.$set(c)},i(o){r||(f(e.$$.fragment,o),f(t.$$.fragment,o),r=!0)},o(o){d(e.$$.fragment,o),d(t.$$.fragment,o),r=!1},d(o){o&&m(n),y(e,o),y(t,o)}}}function ot(s){let e,n=(s[4]!==void 0?s[5][s[4]].description:s[2].description)+"";return{c(){e=E(n)},m(t,r){p(t,e,r)},p(t,r){20&r&&n!==(n=(t[4]!==void 0?t[5][t[4]].description:t[2].description)+"")&&Y(e,n)},d(t){t&&m(e)}}}function rt(s){let e,n,t,r=q(s[5]),o=[];for(let l=0;l<r.length;l+=1)o[l]=L(G(s,r,l));const i=l=>d(o[l],1,1,()=>{o[l]=null});let c=(s[4]!==void 0||s[2])&&M(s);return{c(){for(let l=0;l<o.length;l+=1)o[l].c();e=N(),c&&c.c(),n=U()},m(l,a){for(let $=0;$<o.length;$+=1)o[$]&&o[$].m(l,a);p(l,e,a),c&&c.m(l,a),p(l,n,a),t=!0},p(l,a){if(100&a){let $;for(r=q(l[5]),$=0;$<r.length;$+=1){const v=G(l,r,$);o[$]?(o[$].p(v,a),f(o[$],1)):(o[$]=L(v),o[$].c(),f(o[$],1),o[$].m(e.parentNode,e))}for(R(),$=r.length;$<o.length;$+=1)i($);S()}l[4]!==void 0||l[2]?c?(c.p(l,a),20&a&&f(c,1)):(c=M(l),c.c(),f(c,1),c.m(n.parentNode,n)):c&&(R(),d(c,1,1,()=>{c=null}),S())},i(l){if(!t){for(let a=0;a<r.length;a+=1)f(o[a]);f(c),t=!0}},o(l){o=o.filter(Boolean);for(let a=0;a<o.length;a+=1)d(o[a]);d(c),t=!1},d(l){l&&(m(e),m(n)),j(o,l),c&&c.d(l)}}}function st(s){let e,n,t,r;return e=new x.Trigger({props:{$$slots:{default:[et]},$$scope:{ctx:s}}}),t=new x.Content({props:{side:"bottom",align:"start",$$slots:{default:[rt]},$$scope:{ctx:s}}}),{c(){g(e.$$.fragment),n=N(),g(t.$$.fragment)},m(o,i){h(e,o,i),p(o,n,i),h(t,o,i),r=!0},p(o,i){const c={};1048580&i&&(c.$$scope={dirty:i,ctx:o}),e.$set(c);const l={};1048596&i&&(l.$$scope={dirty:i,ctx:o}),t.$set(l)},i(o){r||(f(e.$$.fragment,o),f(t.$$.fragment,o),r=!0)},o(o){d(e.$$.fragment,o),d(t.$$.fragment,o),r=!1},d(o){o&&m(n),y(e,o),y(t,o)}}}function lt(s){let e;return{c(){e=E("Always")},m(n,t){p(n,e,t)},d(n){n&&m(e)}}}function at(s){let e,n;return e=new k({props:{color:"accent",size:1,disabled:!0,$$slots:{default:[lt]},$$scope:{ctx:s}}}),{c(){g(e.$$.fragment)},m(t,r){h(e,t,r),n=!0},p(t,r){const o={};1048576&r&&(o.$$scope={dirty:r,ctx:t}),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){y(e,t)}}}function ct(s){let e,n,t,r,o;const i=[P,O],c=[];function l(a,$){return 8&$&&(e=null),e==null&&(e=!!a[7](a[3])),e?0:1}return n=l(s,-1),t=c[n]=i[n](s),{c(){t.c(),r=U()},m(a,$){c[n].m(a,$),p(a,r,$),o=!0},p(a,[$]){let v=n;n=l(a,$),n===v?c[n].p(a,$):(R(),d(c[v],1,1,()=>{c[v]=null}),S(),t=c[n],t?t.p(a,$):(t=c[n]=i[n](a),t.c()),f(t,1),t.m(r.parentNode,r))},i(a){o||(f(t),o=!0)},o(a){d(t),o=!1},d(a){a&&m(r),c[n].d(a)}}}function $t(s,e,n){let t,r,o,i,c=T,l=()=>(c(),c=F(w,u=>n(4,i=u)),w);s.$$.on_destroy.push(()=>c());let{onSave:a}=e,{rule:$}=e;const v=[{label:"Always",value:A.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:A.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:A.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let w;l();let b=()=>{};async function C(u){b();try{await a(u.value,u.value!==A.AGENT_REQUESTED||$.description?$.description:"Example description")}catch(z){console.error("RulesModeSelector: Error in onSave:",z)}}return s.$$set=u=>{"onSave"in u&&n(8,a=u.onSave),"rule"in u&&n(9,$=u.rule)},s.$$.update=()=>{512&s.$$.dirty&&n(3,t=$.path),512&s.$$.dirty&&n(10,r=$.type),1024&s.$$.dirty&&n(2,o=v.find(u=>u.value===r))},[w,b,o,t,i,v,C,function(u){return u===Z},a,$,r,u=>C(u),function(u){b=u,n(1,b)},function(u){w=u,l(n(0,w))}]}class ht extends B{constructor(e){super(),Q(this,e,$t,ct,W,{onSave:8,rule:9})}}export{ht as R};
