import{S as J,i as K,s as Q,a5 as no,a6 as io,D as U,E as j,d as M,f as R,F as P,a7 as so,w as d,u as h,k as D,G as _,a1 as S,a as q,l as to,N as X,t as Y,v as Z,W as m,j as oo,af as eo,J as w,K as O,L,M as N,V as A,I as lo,aj as co,ak as ao,n as ro}from"./SpinnerAugment-uKUHz-bK.js";import{g as uo,R as po,G as $o}from"./go-to-website-mini-B1akcZ0N.js";import{I as fo}from"./IconButtonAugment-CQzh_Hae.js";import{a as mo,T as ho}from"./CardAugment-BqjOeIg4.js";import{B as Co}from"./ButtonAugment-D5QDitBR.js";const go=e=>({}),E=e=>({slot:"iconLeft"}),yo=e=>({}),G=e=>({slot:"iconRight"}),ko=e=>({}),I=e=>({}),vo=e=>({}),W=e=>({});function xo(e){let o,i;const n=[e[6],{color:e[2]},{variant:e[5]}];let s={$$slots:{iconRight:[No],iconLeft:[Lo],default:[Oo]},$$scope:{ctx:e}};for(let t=0;t<n.length;t+=1)s=q(s,n[t]);return o=new Co({props:s}),o.$on("click",e[8]),o.$on("keyup",e[27]),o.$on("keydown",e[28]),o.$on("mousedown",e[29]),o.$on("mouseover",e[30]),o.$on("focus",e[31]),o.$on("mouseleave",e[32]),o.$on("blur",e[33]),o.$on("contextmenu",e[34]),{c(){j(o.$$.fragment)},m(t,c){P(o,t,c),i=!0},p(t,c){const a=100&c[0]?oo(n,[64&c[0]&&eo(t[6]),4&c[0]&&{color:t[2]},32&c[0]&&{variant:t[5]}]):{};32&c[1]&&(a.$$scope={dirty:c,ctx:t}),o.$set(a)},i(t){i||(d(o.$$.fragment,t),i=!0)},o(t){h(o.$$.fragment,t),i=!1},d(t){_(o,t)}}}function wo(e){let o,i;const n=[e[6],{color:e[2]},{variant:e[5]}];let s={$$slots:{default:[bo]},$$scope:{ctx:e}};for(let t=0;t<n.length;t+=1)s=q(s,n[t]);return o=new fo({props:s}),o.$on("click",e[8]),o.$on("keyup",e[19]),o.$on("keydown",e[20]),o.$on("mousedown",e[21]),o.$on("mouseover",e[22]),o.$on("focus",e[23]),o.$on("mouseleave",e[24]),o.$on("blur",e[25]),o.$on("contextmenu",e[26]),{c(){j(o.$$.fragment)},m(t,c){P(o,t,c),i=!0},p(t,c){const a=100&c[0]?oo(n,[64&c[0]&&eo(t[6]),4&c[0]&&{color:t[2]},32&c[0]&&{variant:t[5]}]):{};32&c[1]&&(a.$$scope={dirty:c,ctx:t}),o.$set(a)},i(t){i||(d(o.$$.fragment,t),i=!0)},o(t){h(o.$$.fragment,t),i=!1},d(t){_(o,t)}}}function Oo(e){let o;const i=e[18].default,n=w(i,e,e[36],null);return{c(){n&&n.c()},m(s,t){n&&n.m(s,t),o=!0},p(s,t){n&&n.p&&(!o||32&t[1])&&O(n,i,s,s[36],o?N(i,s[36],t,null):L(s[36]),null)},i(s){o||(d(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function Lo(e){let o;const i=e[18].iconLeft,n=w(i,e,e[36],E);return{c(){n&&n.c()},m(s,t){n&&n.m(s,t),o=!0},p(s,t){n&&n.p&&(!o||32&t[1])&&O(n,i,s,s[36],o?N(i,s[36],t,go):L(s[36]),E)},i(s){o||(d(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function No(e){let o;const i=e[18].iconRight,n=w(i,e,e[36],G);return{c(){n&&n.c()},m(s,t){n&&n.m(s,t),o=!0},p(s,t){n&&n.p&&(!o||32&t[1])&&O(n,i,s,s[36],o?N(i,s[36],t,yo):L(s[36]),G)},i(s){o||(d(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function bo(e){let o,i,n;const s=e[18].iconLeft,t=w(s,e,e[36],W),c=e[18].default,a=w(c,e,e[36],null),p=e[18].iconRight,$=w(p,e,e[36],I);return{c(){t&&t.c(),o=A(),a&&a.c(),i=A(),$&&$.c()},m(u,f){t&&t.m(u,f),R(u,o,f),a&&a.m(u,f),R(u,i,f),$&&$.m(u,f),n=!0},p(u,f){t&&t.p&&(!n||32&f[1])&&O(t,s,u,u[36],n?N(s,u[36],f,vo):L(u[36]),W),a&&a.p&&(!n||32&f[1])&&O(a,c,u,u[36],n?N(c,u[36],f,null):L(u[36]),null),$&&$.p&&(!n||32&f[1])&&O($,p,u,u[36],n?N(p,u[36],f,ko):L(u[36]),I)},i(u){n||(d(t,u),d(a,u),d($,u),n=!0)},o(u){h(t,u),h(a,u),h($,u),n=!1},d(u){u&&(D(o),D(i)),t&&t.d(u),a&&a.d(u),$&&$.d(u)}}}function zo(e){let o,i,n,s;const t=[wo,xo],c=[];function a(p,$){return p[0]?0:1}return o=a(e),i=c[o]=t[o](e),{c(){i.c(),n=X()},m(p,$){c[o].m(p,$),R(p,n,$),s=!0},p(p,$){let u=o;o=a(p),o===u?c[o].p(p,$):(Y(),h(c[u],1,1,()=>{c[u]=null}),Z(),i=c[o],i?i.p(p,$):(i=c[o]=t[o](p),i.c()),d(i,1),i.m(n.parentNode,n))},i(p){s||(d(i),s=!0)},o(p){h(i),s=!1},d(p){p&&D(n),c[o].d(p)}}}function Fo(e){let o,i,n,s;function t(a){e[35](a)}let c={onOpenChange:e[7],content:e[4],triggerOn:[mo.Hover],nested:e[1],$$slots:{default:[zo]},$$scope:{ctx:e}};return e[3]!==void 0&&(c.requestClose=e[3]),i=new ho({props:c}),no.push(()=>io(i,"requestClose",t)),{c(){o=U("div"),j(i.$$.fragment),M(o,"class","c-successful-button svelte-1dvyzw2")},m(a,p){R(a,o,p),P(i,o,null),s=!0},p(a,p){const $={};16&p[0]&&($.content=a[4]),2&p[0]&&($.nested=a[1]),101&p[0]|32&p[1]&&($.$$scope={dirty:p,ctx:a}),!n&&8&p[0]&&(n=!0,$.requestClose=a[3],so(()=>n=!1)),i.$set($)},i(a){s||(d(i.$$.fragment,a),s=!0)},o(a){h(i.$$.fragment,a),s=!1},d(a){a&&D(o),_(i)}}}function To(e,o,i){let n,s,t;const c=["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"];let a,p,$=S(o,c),{$$slots:u={},$$scope:f}=o,{defaultColor:k}=o,{tooltip:C}=o,{stateVariant:v}=o,{onClick:b}=o,{tooltipDuration:z=1500}=o,{icon:V=!1}=o,{stickyColor:x=!0}=o,{persistOnTooltipClose:F=!1}=o,{tooltipNested:y}=o,g="neutral",r=k,T=C==null?void 0:C.neutral;return e.$$set=l=>{o=q(q({},o),to(l)),i(38,$=S(o,c)),"defaultColor"in l&&i(9,k=l.defaultColor),"tooltip"in l&&i(10,C=l.tooltip),"stateVariant"in l&&i(11,v=l.stateVariant),"onClick"in l&&i(12,b=l.onClick),"tooltipDuration"in l&&i(13,z=l.tooltipDuration),"icon"in l&&i(0,V=l.icon),"stickyColor"in l&&i(14,x=l.stickyColor),"persistOnTooltipClose"in l&&i(15,F=l.persistOnTooltipClose),"tooltipNested"in l&&i(1,y=l.tooltipNested),"$$scope"in l&&i(36,f=l.$$scope)},e.$$.update=()=>{i(17,{variant:n,...s}=$,n,(i(6,s),i(38,$))),198656&e.$$.dirty[0]&&i(5,t=(v==null?void 0:v[g])??n),66048&e.$$.dirty[0]&&i(2,r=g==="success"?"success":g==="failure"?"error":k)},[V,y,r,a,T,t,s,function(l){F||l||(clearTimeout(p),p=void 0,i(4,T=C==null?void 0:C.neutral),x||i(16,g="neutral"))},async function(l){try{i(16,g=await b(l)??"neutral")}catch{i(16,g="failure")}i(4,T=C==null?void 0:C[g]),clearTimeout(p),p=setTimeout(()=>{a==null||a(),x||i(16,g="neutral")},z)},k,C,v,b,z,x,F,g,n,u,function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){m.call(this,e,l)},function(l){a=l,i(3,a)},f]}class Ro extends J{constructor(o){super(),K(this,o,To,Fo,Q,{defaultColor:9,tooltip:10,stateVariant:11,onClick:12,tooltipDuration:13,icon:0,stickyColor:14,persistOnTooltipClose:15,tooltipNested:1},null,[-1,-1])}}const Do=e=>({}),B=e=>({});function H(e){let o,i,n,s;return i=new Ro({props:{defaultColor:e[1],stickyColor:e[3],size:e[0],variant:e[2],tooltip:e[4]||void 0,stateVariant:{success:"soft"},onClick:e[5],icon:!e[8].text,$$slots:{iconLeft:[jo],default:[Vo]},$$scope:{ctx:e}}}),{c(){o=U("span"),j(i.$$.fragment),M(o,"class",n="c-open-file-button-container c-open-file-button__size--"+e[0]+" svelte-pdfhuj")},m(t,c){R(t,o,c),P(i,o,null),s=!0},p(t,c){const a={};2&c&&(a.defaultColor=t[1]),8&c&&(a.stickyColor=t[3]),1&c&&(a.size=t[0]),4&c&&(a.variant=t[2]),16&c&&(a.tooltip=t[4]||void 0),32&c&&(a.onClick=t[5]),256&c&&(a.icon=!t[8].text),131072&c&&(a.$$scope={dirty:c,ctx:t}),i.$set(a),(!s||1&c&&n!==(n="c-open-file-button-container c-open-file-button__size--"+t[0]+" svelte-pdfhuj"))&&M(o,"class",n)},i(t){s||(d(i.$$.fragment,t),s=!0)},o(t){h(i.$$.fragment,t),s=!1},d(t){t&&D(o),_(i)}}}function Vo(e){let o;const i=e[16].text,n=w(i,e,e[17],B);return{c(){n&&n.c()},m(s,t){n&&n.m(s,t),o=!0},p(s,t){n&&n.p&&(!o||131072&t)&&O(n,i,s,s[17],o?N(i,s[17],t,Do):L(s[17]),B)},i(s){o||(d(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function jo(e){let o,i;return o=new $o({props:{slot:"iconLeft"}}),{c(){j(o.$$.fragment)},m(n,s){P(o,n,s),i=!0},p:ro,i(n){i||(d(o.$$.fragment,n),i=!0)},o(n){h(o.$$.fragment,n),i=!1},d(n){_(o,n)}}}function Po(e){let o,i,n=e[6]&&H(e);return{c(){n&&n.c(),o=X()},m(s,t){n&&n.m(s,t),R(s,o,t),i=!0},p(s,[t]){s[6]?n?(n.p(s,t),64&t&&d(n,1)):(n=H(s),n.c(),d(n,1),n.m(o.parentNode,o)):n&&(Y(),h(n,1,1,()=>{n=null}),Z())},i(s){i||(d(n),i=!0)},o(s){h(n),i=!1},d(s){s&&D(o),n&&n.d(s)}}}function _o(e,o,i){let n,s,t,c,{$$slots:a={},$$scope:p}=o;const $=lo(a);let{path:u}=o,{start:f=0}=o,{stop:k=0}=o,{size:C=0}=o,{color:v="neutral"}=o,{variant:b="ghost-block"}=o,{stickyColor:z=!1}=o,{tooltip:V={neutral:"Open File In Editor",success:"Opening file..."}}=o,{onOpenLocalFile:x=async function(r){var T,l;if((T=r==null?void 0:r.stopPropagation)==null||T.call(r),(l=r==null?void 0:r.preventDefault)==null||l.call(r),u)return await F(),"success"}}=o;const F=async()=>{const r=await(y==null?void 0:y.extensionClient.resolvePath({rootPath:"",relPath:u}));return y==null?void 0:y.extensionClient.openFile({repoRoot:(r==null?void 0:r.repoRoot)??"",pathName:(r==null?void 0:r.pathName)??"",range:{start:Math.max(f,0),stop:Math.max(k,0)}})},y=uo(),g=co(po.key);return ao(e,g,r=>i(15,c=r)),e.$$set=r=>{"path"in r&&i(9,u=r.path),"start"in r&&i(10,f=r.start),"stop"in r&&i(11,k=r.stop),"size"in r&&i(0,C=r.size),"color"in r&&i(1,v=r.color),"variant"in r&&i(2,b=r.variant),"stickyColor"in r&&i(3,z=r.stickyColor),"tooltip"in r&&i(4,V=r.tooltip),"onOpenLocalFile"in r&&i(5,x=r.onOpenLocalFile),"$$scope"in r&&i(17,p=r.$$scope)},e.$$.update=()=>{32768&e.$$.dirty&&i(13,n=c==null?void 0:c.isRemoteAgentSshWindow),32768&e.$$.dirty&&i(14,s=!!(c!=null&&c.isActive)),24576&e.$$.dirty&&i(6,t=!s||n)},[C,v,b,z,V,x,t,g,$,u,f,k,F,n,s,c,a,p]}class Io extends J{constructor(o){super(),K(this,o,_o,Po,Q,{path:9,start:10,stop:11,size:0,color:1,variant:2,stickyColor:3,tooltip:4,onOpenLocalFile:5,openFile:12})}get openFile(){return this.$$.ctx[12]}}export{Io as O,Ro as S};
