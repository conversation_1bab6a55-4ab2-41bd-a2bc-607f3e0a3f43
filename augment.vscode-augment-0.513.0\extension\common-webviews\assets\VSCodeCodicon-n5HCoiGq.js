import{S as e,i as l,s as d,D as u,d as t,f as p,n as r,k as f}from"./SpinnerAugment-uKUHz-bK.js";function m(a){let s,o;return{c(){s=u("span"),t(s,"class",o="codicon codicon-"+a[0]+" "+a[1])},m(c,n){p(c,s,n)},p(c,[n]){3&n&&o!==(o="codicon codicon-"+c[0]+" "+c[1])&&t(s,"class",o)},i:r,o:r,d(c){c&&f(s)}}}function x(a,s,o){let{icon:c}=s,{class:n=""}=s;return a.$$set=i=>{"icon"in i&&o(0,c=i.icon),"class"in i&&o(1,n=i.class)},[c,n]}class h extends e{constructor(s){super(),l(this,s,x,m,d,{icon:0,class:1})}}export{h as V};
