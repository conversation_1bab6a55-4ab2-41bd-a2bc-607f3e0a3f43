import{S as u,i as f,s as g,a as i,b as C,d as r,e as h,f as c,h as p,j as v,n as w,k as x,l as d}from"./SpinnerAugment-uKUHz-bK.js";function L(n){let s,e,l=[{width:"15"},{height:"15"},{viewBox:"0 0 15 15"},{fill:"none"},{xmlns:"http://www.w3.org/2000/svg"},n[0]],o={};for(let t=0;t<l.length;t+=1)o=i(o,l[t]);return{c(){s=C("svg"),e=C("path"),r(e,"fill-rule","evenodd"),r(e,"clip-rule","evenodd"),r(e,"d","M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"),r(e,"fill","currentColor"),h(s,o)},m(t,a){c(t,s,a),p(s,e)},p(t,[a]){h(s,o=v(l,[{width:"15"},{height:"15"},{viewBox:"0 0 15 15"},{fill:"none"},{xmlns:"http://www.w3.org/2000/svg"},1&a&&t[0]]))},i:w,o:w,d(t){t&&x(s)}}}function m(n,s,e){return n.$$set=l=>{e(0,s=i(i({},s),d(l)))},[s=d(s)]}class Z extends u{constructor(s){super(),f(this,s,m,L,g,{})}}export{Z as E};
