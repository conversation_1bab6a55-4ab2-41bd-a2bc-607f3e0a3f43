var Ue=Object.defineProperty;var He=(n,e,t)=>e in n?Ue(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var o=(n,e,t)=>He(n,typeof e!="symbol"?e+"":e,t);import{g as Fe,h as De,b as Oe,c as Pe,p as Le,f as S,i as ie,j as Ne,s as ze,k as K,l as oe,F as Be,m as me,n as J,C as je,o as We,q as j,W as $e,r as w,R as Ge,t as Q,u as pe,v as Z,w as ye,x as ee,E as te,y as se,z as Ve,B as Ye}from"./index-DP6mqmYw.js";import{a as Xe}from"./async-messaging-D4p6YcQf.js";import{W as h}from"./IconButtonAugment-CQzh_Hae.js";import{P as C,C as v,b as ne,I as Y,a as I,E as Ke}from"./message-broker-DdVtH9Vr.js";import{C as Je}from"./types-CGlLNakm.js";import{n as Qe,f as Ze,i as et}from"./file-paths-CAgP5Fvb.js";import{C as tt,S as st,i as nt,s as at,a as ce,b as rt,H as it,y as ot,z as ct,A as dt,k as _e,e as fe,B as ut,j as lt,n as Se,l as ve}from"./SpinnerAugment-uKUHz-bK.js";var H;function Te(n){const e=H[n];return typeof e!="string"?n.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,t=>"_"+t.toLowerCase())}(function(n){n[n.Canceled=1]="Canceled",n[n.Unknown=2]="Unknown",n[n.InvalidArgument=3]="InvalidArgument",n[n.DeadlineExceeded=4]="DeadlineExceeded",n[n.NotFound=5]="NotFound",n[n.AlreadyExists=6]="AlreadyExists",n[n.PermissionDenied=7]="PermissionDenied",n[n.ResourceExhausted=8]="ResourceExhausted",n[n.FailedPrecondition=9]="FailedPrecondition",n[n.Aborted=10]="Aborted",n[n.OutOfRange=11]="OutOfRange",n[n.Unimplemented=12]="Unimplemented",n[n.Internal=13]="Internal",n[n.Unavailable=14]="Unavailable",n[n.DataLoss=15]="DataLoss",n[n.Unauthenticated=16]="Unauthenticated"})(H||(H={}));class U extends Error{constructor(e,t=H.Unknown,s,a,r){super(function(i,c){return i.length?`[${Te(c)}] ${i}`:`[${Te(c)}]`}(e,t)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=t,this.metadata=new Headers(s??{}),this.details=a??[],this.cause=r}static from(e,t=H.Unknown){return e instanceof U?e:e instanceof Error?e.name=="AbortError"?new U(e.message,H.Canceled):new U(e.message,t,void 0,void 0,e):new U(String(e),t,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===U.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const t=e.kind==="message"?{getMessage:a=>a===e.typeName?e:void 0}:e,s=[];for(const a of this.details){if("desc"in a){t.getMessage(a.desc.typeName)&&s.push(Fe(a.desc,a.value));continue}const r=t.getMessage(a.type);if(r)try{s.push(De(r,a.value))}catch{}}return s}}var ht=function(n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,t=n[Symbol.asyncIterator];return t?t.call(n):(n=typeof __values=="function"?__values(n):n[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(a){e[a]=n[a]&&function(r){return new Promise(function(i,c){(function(u,m,y,d){Promise.resolve(d).then(function(l){u({value:l,done:y})},m)})(i,c,(r=n[a](r)).done,r.value)})}}},V=function(n){return this instanceof V?(this.v=n,this):new V(n)},gt=function(n,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,a=t.apply(n,e||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(d){return function(l){return Promise.resolve(l).then(d,m)}}),s[Symbol.asyncIterator]=function(){return this},s;function i(d,l){a[d]&&(s[d]=function(g){return new Promise(function(p,x){r.push([d,g,p,x])>1||c(d,g)})},l&&(s[d]=l(s[d])))}function c(d,l){try{(g=a[d](l)).value instanceof V?Promise.resolve(g.value.v).then(u,m):y(r[0][2],g)}catch(p){y(r[0][3],p)}var g}function u(d){c("next",d)}function m(d){c("throw",d)}function y(d,l){d(l),r.shift(),r.length&&c(r[0][0],r[0][1])}},mt=function(n){var e,t;return e={},s("next"),s("throw",function(a){throw a}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(a,r){e[a]=n[a]?function(i){return(t=!t)?{value:V(n[a](i)),done:!1}:r?r(i):i}:r}},be=function(n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,t=n[Symbol.asyncIterator];return t?t.call(n):(n=typeof __values=="function"?__values(n):n[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(a){e[a]=n[a]&&function(r){return new Promise(function(i,c){(function(u,m,y,d){Promise.resolve(d).then(function(l){u({value:l,done:y})},m)})(i,c,(r=n[a](r)).done,r.value)})}}},L=function(n){return this instanceof L?(this.v=n,this):new L(n)},pt=function(n){var e,t;return e={},s("next"),s("throw",function(a){throw a}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(a,r){e[a]=n[a]?function(i){return(t=!t)?{value:L(n[a](i)),done:!1}:r?r(i):i}:r}},yt=function(n,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,a=t.apply(n,e||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(d){return function(l){return Promise.resolve(l).then(d,m)}}),s[Symbol.asyncIterator]=function(){return this},s;function i(d,l){a[d]&&(s[d]=function(g){return new Promise(function(p,x){r.push([d,g,p,x])>1||c(d,g)})},l&&(s[d]=l(s[d])))}function c(d,l){try{(g=a[d](l)).value instanceof L?Promise.resolve(g.value.v).then(u,m):y(r[0][2],g)}catch(p){y(r[0][3],p)}var g}function u(d){c("next",d)}function m(d){c("throw",d)}function y(d,l){d(l),r.shift(),r.length&&c(r[0][0],r[0][1])}};function _t(n,e){return function(t,s){const a={};for(const r of t.methods){const i=s(r);i!=null&&(a[r.localName]=i)}return a}(n,t=>{switch(t.methodKind){case"unary":return function(s,a){return async function(r,i){var c,u;const m=await s.unary(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);return(c=i==null?void 0:i.onHeader)===null||c===void 0||c.call(i,m.header),(u=i==null?void 0:i.onTrailer)===null||u===void 0||u.call(i,m.trailer),m.message}}(e,t);case"server_streaming":return function(s,a){return function(r,i){return xe(s.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,function(c){return gt(this,arguments,function*(){yield V(yield*mt(ht(c)))})}([r]),i==null?void 0:i.contextValues),i)}}(e,t);case"client_streaming":return function(s,a){return async function(r,i){var c,u,m,y,d,l;const g=await s.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);let p;(d=i==null?void 0:i.onHeader)===null||d===void 0||d.call(i,g.header);let x=0;try{for(var f,q=!0,F=be(g.message);!(c=(f=await F.next()).done);q=!0)y=f.value,q=!1,p=y,x++}catch(N){u={error:N}}finally{try{q||c||!(m=F.return)||await m.call(F)}finally{if(u)throw u.error}}if(!p)throw new U("protocol error: missing response message",H.Unimplemented);if(x>1)throw new U("protocol error: received extra messages for client streaming method",H.Unimplemented);return(l=i==null?void 0:i.onTrailer)===null||l===void 0||l.call(i,g.trailer),p}}(e,t);case"bidi_streaming":return function(s,a){return function(r,i){return xe(s.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues),i)}}(e,t);default:return null}})}function xe(n,e){const t=function(){return yt(this,arguments,function*(){var s,a;const r=yield L(n);(s=e==null?void 0:e.onHeader)===null||s===void 0||s.call(e,r.header),yield L(yield*pt(be(r.message))),(a=e==null?void 0:e.onTrailer)===null||a===void 0||a.call(e,r.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>t.next()})}}function Ie(n,e){return function(t,s){if(t.length<=s||t.length===0)return{truncatedText:t};const a=t.split(`
`),r="... additional lines truncated ..."+(a[0].endsWith("\r")?"\r":"");let i,c="";if(a.length<2||a[0].length+a[a.length-1].length+r.length>s){const u=Math.floor(s/2);c=[t.slice(0,u),"<...>",t.slice(-u)].join(""),i=[1,1,a.length,a.length]}else{const u=[],m=[];let y=r.length+1;for(let d=0;d<Math.floor(a.length/2);d++){const l=a[d],g=a[a.length-1-d],p=l.length+g.length+2;if(y+p>s)break;y+=p,u.push(l),m.push(g)}i=[1,u.length,a.length-m.length+1,a.length],u.push(r),u.push(...m.reverse()),c=u.join(`
`)}return{truncatedText:c,shownRangeWhenTruncated:i}}(n,e).truncatedText}function ft(n,e,t=1e3){let s=null,a=0;const r=tt(e),i=()=>{const c=(()=>{const u=Date.now();if(s!==null&&u-a<t)return s;const m=n();return s=m,a=u,m})();r.set(c)};return{subscribe:r.subscribe,resetCache:()=>{s=null,i()},updateStore:i}}var Re=(n=>(n[n.unset=0]="unset",n[n.positive=1]="positive",n[n.negative=2]="negative",n))(Re||{});function M(n,e){return e in n&&n[e]!==void 0}function St(n){return M(n,"file")}function vt(n){return M(n,"recentFile")}function Tt(n){return M(n,"folder")}function xt(n){return M(n,"sourceFolder")}function ss(n){return M(n,"sourceFolderGroup")}function ns(n){return M(n,"selection")}function It(n){return M(n,"externalSource")}function as(n){return M(n,"allDefaultContext")}function rs(n){return M(n,"clearContext")}function is(n){return M(n,"userGuidelines")}function os(n){return M(n,"agentMemories")}function Ae(n){return M(n,"personality")}function Ct(n){return M(n,"rule")}function wt(n){return M(n,"task")}const cs={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},ds={clearContext:!0,label:"Clear Context",id:"clearContext"},us={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},ls={agentMemories:{},label:"Agent Memories",id:"agentMemories"},Ce=[{personality:{type:C.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:C.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:C.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:C.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function hs(n){return M(n,"group")}function gs(n){const e=new Map;return n.forEach(t=>{St(t)?e.set("file",[...e.get("file")??[],t]):vt(t)?e.set("recentFile",[...e.get("recentFile")??[],t]):Tt(t)?e.set("folder",[...e.get("folder")??[],t]):It(t)?e.set("externalSource",[...e.get("externalSource")??[],t]):xt(t)?e.set("sourceFolder",[...e.get("sourceFolder")??[],t]):Ae(t)?e.set("personality",[...e.get("personality")??[],t]):Ct(t)&&e.set("rule",[...e.get("rule")??[],t])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(t=>t.group.items.length>0)}function Et(n){const e=Le({rootPath:n.repoRoot,relPath:n.pathName}),t={label:Qe(n.pathName).split("/").filter(s=>s.trim()!=="").pop()||"",name:e,id:e};if(n.fullRange){const s=`:L${n.fullRange.startLineNumber}-${n.fullRange.endLineNumber}`;t.label+=s,t.name+=s,t.id+=s}else if(n.range){const s=`:L${n.range.start}-${n.range.stop}`;t.label+=s,t.name+=s,t.id+=s}return t}function Mt(n){const e=n.path.split("/"),t=e[e.length-1],s=t.endsWith(".md")?t.slice(0,-3):t,a=`${Oe}/${Pe}/${n.path}`;return{label:s,name:a,id:a}}function qt(n){var t;if(!n)return Y.IMAGE_FORMAT_UNSPECIFIED;switch((t=n.split("/")[1])==null?void 0:t.toLowerCase()){case"jpeg":case"jpg":return Y.JPEG;case"png":return Y.PNG;default:return Y.IMAGE_FORMAT_UNSPECIFIED}}function bt(n,e,t){var a,r;if(n.phase!==S.cancelled&&n.phase!==S.completed&&n.phase!==S.error)return;let s;return(a=n.result)!=null&&a.contentNodes?(s=function(i,c){return i.map(u=>u.type===ie.ContentText?{type:ne.CONTENT_TEXT,text_content:u.text_content}:u.type===ie.ContentImage&&u.image_content&&c?{type:ne.CONTENT_IMAGE,image_content:{image_data:u.image_content.image_data,format:qt(u.image_content.media_type)}}:{type:ne.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(n.result.contentNodes,t),{content:"",is_error:n.result.isError,request_id:n.result.requestId,tool_use_id:e,content_nodes:s}):((r=n.result)==null?void 0:r.text)!==void 0?{content:n.result.text,is_error:n.result.isError,request_id:n.result.requestId,tool_use_id:e}:void 0}function Rt(n=[]){let e;for(const t of n){if(t.type===v.TOOL_USE)return t;t.type===v.TOOL_USE_START&&(e=t)}return e}function At(n,e,t,s){if(!n||!e)return[];let a=!1;return e.filter(r=>{var c;const i=s!=null&&s.isActive&&r.tool_use?s.getToolUseState(r.tool_use.tool_use_id):t.getToolUseState(r.requestId??n,(c=r.tool_use)==null?void 0:c.tool_use_id);return a===!1&&i.phase!==S.new&&i.phase!==S.unknown&&i.phase!==S.checkingSafety&&r.tool_use!==void 0||(i.phase===S.runnable&&(a=!0),!1)})}function ms(n,e){if(n.contentNodes&&n.contentNodes.length>0){const t=n.contentNodes.map(s=>{if(s.type===ie.ContentText){let a="";return s.text_content&&(a=Ie(s.text_content,e/n.contentNodes.length)),{...s,text_content:a}}return s});return{...n,contentNodes:t}}return{...n,text:Ie(n.text,e)}}const kt="__NEW_AGENT__",ps=n=>n.chatItemType===void 0,ys=(n,e)=>{var r;const t=n.chatHistory.at(-1);if(!t||!T(t))return D.notRunning;if(!(t.status===_.success||t.status===_.failed||t.status===_.cancelled))return D.running;const s=((r=t.structured_output_nodes)==null?void 0:r.filter(i=>i.type===v.TOOL_USE&&!!i.tool_use))??[];let a;if(a=e.enableParallelTools?At(t.request_id,s,n).at(-1):s.at(-1),!a||!a.tool_use)return D.notRunning;switch(n.getToolUseState(t.request_id,a.tool_use.tool_use_id).phase){case S.runnable:return D.awaitingUserAction;case S.cancelled:return D.notRunning;default:return D.running}},de=n=>T(n)&&!!n.request_message,Ut=n=>n.chatHistory.findLast(e=>de(e)),_s=(n,e)=>{const t=Ut(n);return t!=null&&t.request_id?n.historyFrom(t.request_id,!0).filter(s=>T(s)&&(!e||e(s))):[]},fs=n=>{var s;const e=n.chatHistory.at(-1);if(!(e!=null&&e.request_id)||!T(e))return!1;const t=((s=e.structured_output_nodes)==null?void 0:s.filter(a=>a.type===v.TOOL_USE))??[];for(const a of t)if(a.tool_use&&n.getToolUseState(e.request_id,a.tool_use.tool_use_id).phase===S.runnable)return n.updateToolUseState({requestId:e.request_id,toolUseId:a.tool_use.tool_use_id,phase:S.cancelled}),!0;return!1};function Ht(n,e){const t=n.customPersonalityPrompts;if(t)switch(e){case C.DEFAULT:if(t.agent&&t.agent.trim()!=="")return t.agent;break;case C.PROTOTYPER:if(t.prototyper&&t.prototyper.trim()!=="")return t.prototyper;break;case C.BRAINSTORM:if(t.brainstorm&&t.brainstorm.trim()!=="")return t.brainstorm;break;case C.REVIEWER:if(t.reviewer&&t.reviewer.trim()!=="")return t.reviewer}return Ft[e]}const Ft={[C.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[C.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[C.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[C.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};function W(n){var e;return((e=n.extraData)==null?void 0:e.isAgentConversation)===!0}var Dt=(n=>(n[n.active=0]="active",n[n.inactive=1]="inactive",n))(Dt||{});const A={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was summarized to reduce context size.
Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
<summary>
{summary}
</summary>
Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point."};class Ot{constructor(e,t,s){o(this,"historySummaryVersion",2);o(this,"_callbacksManager",new Ne);o(this,"_params");this._conversationModel=e,this._extensionClient=t,this._chatFlagModel=s,this._params=we(s.historySummaryParams),s.subscribe(a=>{this._params=we(a.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}clearStaleHistorySummaryNodes(e){return e.filter(t=>!P(t)||t.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(e){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const t=this._params.cacheTTLMs-e-this._params.bufferTimeBeforeCacheExpirationMs;t>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},t)}preprocessChatHistory(e){const t=e.findLastIndex(s=>P(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(t>0&&(console.info(`Using history summary node found at index ${t} with requestId: ${e[t].request_id}`),e=e.slice(t)),e=e.filter(s=>!P(s)||s.summaryVersion===this.historySummaryVersion)):e=e.filter(s=>!P(s)),e}async maybeAddHistorySummaryNode(e=!1,t){var le,he,ge;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",e),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),a=e?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",a),a<=0)return!1;const{head:r,tail:i,headSizeChars:c,tailSizeChars:u}=ze(s,this._params.historyTailSizeCharsToExclude,a,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",c," tailSizeChars: ",u),r.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const m=K(s),y=K(r),d=K(i),l={totalHistoryCharCount:m,totalHistoryExchangeCount:s.length,headCharCount:y,headExchangeCount:r.length,headLastRequestId:((le=r.at(-1))==null?void 0:le.request_id)??"",tailCharCount:d,tailExchangeCount:i.length,tailLastRequestId:((he=i.at(-1))==null?void 0:he.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:e,isAborted:!1};let g=((ge=r.at(-1))==null?void 0:ge.response_nodes)??[],p=g.filter(k=>k.type===v.TOOL_USE);p.length>0&&(r.at(-1).response_nodes=g.filter(k=>k.type!==v.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",r.length);const x=Date.now(),{responseText:f,requestId:q}=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:r}),F=Date.now();if(l.summaryCharCount=f.length,l.summarizationDurationMs=F-x,l.isAborted=!!(t!=null&&t.aborted),this._extensionClient.reportAgentRequestEvent({eventName:oe.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:q??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:l}}),t==null?void 0:t.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!q||f.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;const N=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",f),z=this._params.summaryNodeResponseMessage,B={chatItemType:O.historySummary,summaryVersion:this.historySummaryVersion,request_id:q,request_message:N,response_text:z,structured_output_nodes:[{id:p.map(k=>k.id).reduce((k,ke)=>Math.max(k,ke),-1)+1,type:v.RAW_RESPONSE,content:z},...p],status:_.success,seen_state:E.seen,timestamp:new Date().toISOString()},b=this._conversationModel.chatHistory.findLastIndex(k=>k.request_id===r.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",b),this._conversationModel.insertChatItem(b,B),!0}}function we(n){try{if(!n)return console.log("historySummaryParams is empty. Using default params"),A;const e=JSON.parse(n),t={triggerOnHistorySizeChars:e.trigger_on_history_size_chars||A.triggerOnHistorySizeChars,historyTailSizeCharsToExclude:e.history_tail_size_chars_to_exclude||A.historyTailSizeCharsToExclude,triggerOnHistorySizeCharsWhenCacheExpiring:e.trigger_on_history_size_chars_when_cache_expiring||A.triggerOnHistorySizeCharsWhenCacheExpiring,prompt:e.prompt||A.prompt,cacheTTLMs:e.cache_ttl_ms||A.cacheTTLMs,bufferTimeBeforeCacheExpirationMs:e.buffer_time_before_cache_expiration_ms||A.bufferTimeBeforeCacheExpirationMs,summaryNodeRequestMessageTemplate:e.summary_node_request_message_template||A.summaryNodeRequestMessageTemplate,summaryNodeResponseMessage:e.summary_node_response_message||A.summaryNodeResponseMessage};t.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),t.summaryNodeRequestMessageTemplate=A.summaryNodeRequestMessageTemplate);const s={...t,prompt:t.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",s),t}catch(e){return console.error("Failed to parse history_summary_params:",e),A}}const X="temp-fe";class R{constructor(e,t,s,a){o(this,"_state");o(this,"_subscribers",new Set);o(this,"_focusModel",new Be);o(this,"_onSendExchangeListeners",[]);o(this,"_onNewConversationListeners",[]);o(this,"_onHistoryDeleteListeners",[]);o(this,"_onBeforeChangeConversationListeners",[]);o(this,"_totalCharactersCacheThrottleMs",1e3);o(this,"_totalCharactersStore");o(this,"_chatHistorySummarizationModel");o(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));o(this,"setConversation",(e,t=!0,s=!0)=>{const a=e.id!==this._state.id;a&&s&&(e.toolUseStates=Object.fromEntries(Object.entries(e.toolUseStates??{}).map(([i,c])=>{if(c.requestId&&c.toolUseId){const{requestId:u,toolUseId:m}=me(i);return u===c.requestId&&m===c.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",i,"but object has ",J(c)),[i,c]}return[i,{...c,...me(i)}]})),(e=this._notifyBeforeChangeConversation(this._state,e)).lastInteractedAtIso=new Date().toISOString()),t&&a&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const r=R.isEmpty(e);if(a&&r){const i=this._state.draftExchange;i&&(e.draftExchange=i)}return this._state=e,this._focusModel.setItems(this._state.chatHistory.filter(T)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(i=>i(this)),this._saveConversation(this._state),a&&(this._loadContextFromConversation(e),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(i=>i())),!0});o(this,"update",e=>{this.setConversation({...this._state,...e}),this._totalCharactersStore.updateStore()});o(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});o(this,"setName",e=>{this.update({name:e})});o(this,"setSelectedModelId",e=>{this.update({selectedModelId:e})});o(this,"updateFeedback",(e,t)=>{this.update({feedbackStates:{...this._state.feedbackStates,[e]:t}})});o(this,"updateToolUseState",e=>{this.update({toolUseStates:{...this._state.toolUseStates,[J(e)]:e}})});o(this,"getToolUseState",(e,t)=>e===void 0||t===void 0||this.toolUseStates===void 0?{phase:S.unknown,requestId:e??"",toolUseId:t??""}:this.toolUseStates[J({requestId:e,toolUseId:t})]||{phase:S.new});o(this,"getLastToolUseId",()=>{var s,a;const e=this.lastExchange;if(!e)return;const t=(((s=e==null?void 0:e.structured_output_nodes)==null?void 0:s.filter(r=>r.type===v.TOOL_USE))??[]).at(-1);return t?(a=t.tool_use)==null?void 0:a.tool_use_id:void 0});o(this,"getLastToolUseState",()=>{var s;const e=this.lastExchange;if(!e)return{phase:S.unknown};const t=function(a=[]){let r;for(const i of a){if(i.type===v.TOOL_USE)return i;i.type===v.TOOL_USE_START&&(r=i)}return r}(e==null?void 0:e.structured_output_nodes);return t?this.getToolUseState(e.request_id,(s=t.tool_use)==null?void 0:s.tool_use_id):{phase:S.unknown}});o(this,"addExchange",(e,t)=>{const s=this._state.chatHistory;let a,r;a=t===void 0?[...s,e]:t===-1?s.length===0?[e]:[...s.slice(0,-1),e,s[s.length-1]]:[...s.slice(0,t),e,...s.slice(t)],T(e)&&(r=e.request_id?{...this._state.feedbackStates,[e.request_id]:{selectedRating:Re.unset,feedbackNote:""}}:void 0),this.update({chatHistory:a,...r?{feedbackStates:r}:{},lastUrl:void 0})});o(this,"addExchangeBeforeLast",e=>{this.addExchange(e,-1)});o(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});o(this,"updateExchangeById",(e,t,s=!1)=>{var c;const a=this.exchangeWithRequestId(t);if(a===null)return console.warn("No exchange with this request ID found."),!1;s&&e.response_text!==void 0&&(e.response_text=(a.response_text??"")+(e.response_text??"")),s&&(e.structured_output_nodes=function(u=[]){const m=Rt(u);return m&&m.type===v.TOOL_USE?u.filter(y=>y.type!==v.TOOL_USE_START):u}([...a.structured_output_nodes??[],...e.structured_output_nodes??[]])),e.stop_reason!==a.stop_reason&&a.stop_reason&&e.stop_reason===Je.REASON_UNSPECIFIED&&(e.stop_reason=a.stop_reason),s&&e.workspace_file_chunks!==void 0&&(e.workspace_file_chunks=[...a.workspace_file_chunks??[],...e.workspace_file_chunks??[]]);const r=(c=(e.structured_output_nodes||[]).find(u=>u.type===v.MAIN_TEXT_FINISHED))==null?void 0:c.content;r&&r!==e.response_text&&(e.response_text=r);let i=this._state.isShareable||ae({...a,...e});return this.update({chatHistory:this.chatHistory.map(u=>u.request_id===t?{...u,...e}:u),isShareable:i}),!0});o(this,"clearMessagesFromHistory",e=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&e.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!e.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(e),toolUseIds:t})});o(this,"clearHistory",()=>{const e=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:e}),this.update({chatHistory:[]})});o(this,"clearHistoryFrom",async(e,t=!0)=>{const s=this.historyFrom(e,t),a=s.map(i=>i.request_id).filter(i=>i!==void 0),r=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(e,!t)}),this._extensionClient.clearMetadataFor({requestIds:a,toolUseIds:r}),s.forEach(i=>{this._onHistoryDeleteListeners.forEach(c=>c(i))})});o(this,"clearMessageFromHistory",e=>{const t=this.chatHistory.find(a=>a.request_id===e),s=t?this._collectToolUseIdsFromMessages([t]):[];this.update({chatHistory:this.chatHistory.filter(a=>a.request_id!==e)}),this._extensionClient.clearMetadataFor({requestIds:[e],toolUseIds:s})});o(this,"_collectToolUseIdsFromMessages",e=>{var s;const t=[];for(const a of e)if(T(a)&&a.structured_output_nodes)for(const r of a.structured_output_nodes)r.type===v.TOOL_USE&&((s=r.tool_use)!=null&&s.tool_use_id)&&t.push(r.tool_use.tool_use_id);return t});o(this,"historyTo",(e,t=!1)=>{const s=this.chatHistory.findIndex(a=>a.request_id===e);return s===-1?[]:this.chatHistory.slice(0,t?s+1:s)});o(this,"historyFrom",(e,t=!0)=>{const s=this.chatHistory.findIndex(a=>a.request_id===e);return s===-1?[]:this.chatHistory.slice(t?s:s+1)});o(this,"resendLastExchange",async()=>{const e=this.lastExchange;if(e&&!this.awaitingReply)return this.resendTurn(e)});o(this,"resendTurn",e=>this.awaitingReply?Promise.resolve():(this._removeTurn(e),this.sendExchange({chatItemType:e.chatItemType,request_message:e.request_message,rich_text_json_repr:e.rich_text_json_repr,status:_.draft,mentioned_items:e.mentioned_items,structured_request_nodes:e.structured_request_nodes,disableSelectedCodeDetails:e.disableSelectedCodeDetails,chatHistory:e.chatHistory,model_id:e.model_id},!1,e.request_id)));o(this,"_removeTurn",e=>{this.update({chatHistory:this.chatHistory.filter(t=>t!==e&&(!e.request_id||t.request_id!==e.request_id))})});o(this,"exchangeWithRequestId",e=>this.chatHistory.find(t=>t.request_id===e)||null);o(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});o(this,"markSeen",async e=>{if(!e.request_id||!this.chatHistory.find(s=>s.request_id===e.request_id))return;const t={seen_state:E.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===e.request_id?{...s,...t}:s)})});o(this,"createStructuredRequestNodes",e=>this._jsonToStructuredRequest(e));o(this,"saveDraftMentions",e=>{if(!this.draftExchange)return;const t=e.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:t}})});o(this,"saveDraftActiveContextIds",()=>{const e=this._specialContextInputModel.recentActiveItems.map(t=>t.id);this.update({draftActiveContextIds:e})});o(this,"loadDraftActiveContextIds",()=>{const e=new Set(this.draftActiveContextIds??[]),t=this._specialContextInputModel.recentItems.filter(a=>e.has(a.id)||a.recentFile||a.selection||a.sourceFolder),s=this._specialContextInputModel.recentItems.filter(a=>!(e.has(a.id)||a.recentFile||a.selection||a.sourceFolder));this._specialContextInputModel.markItemsActive(t.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});o(this,"saveDraftExchange",(e,t)=>{var i,c,u;const s=e!==((i=this.draftExchange)==null?void 0:i.request_message),a=t!==((c=this.draftExchange)==null?void 0:c.rich_text_json_repr);if(!s&&!a)return;const r=(u=this.draftExchange)==null?void 0:u.mentioned_items;this.update({draftExchange:{request_message:e,rich_text_json_repr:t,mentioned_items:r,status:_.draft}})});o(this,"clearDraftExchange",()=>{const e=this.draftExchange;return this.update({draftExchange:void 0}),e});o(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const e=this.clearDraftExchange();if(!e)return!1;const t=this._chatFlagModel.enableChatMultimodal&&e.rich_text_json_repr?this._jsonToStructuredRequest(e.rich_text_json_repr):void 0;return this.sendExchange({...e,structured_request_nodes:t,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!W(this)){const a=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&a&&this.updateConversationTitle()}}).finally(()=>{var s;W(this)&&this._extensionClient.reportAgentRequestEvent({eventName:oe.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});o(this,"cancelMessage",async()=>{var e;this.canCancelMessage&&((e=this.lastExchange)!=null&&e.request_id)&&(this.updateExchangeById({status:_.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});o(this,"sendInstructionExchange",async(e,t)=>{let s=`${X}-${crypto.randomUUID()}`;const a={status:_.sent,request_id:s,request_message:e,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:E.unseen,timestamp:new Date().toISOString()};this.addExchange(a);for await(const r of this._extensionClient.sendInstructionMessage(a,t)){if(!this.updateExchangeById(r,s,!0))return;s=r.request_id||s}});o(this,"updateConversationTitle",async()=>{const{responseText:e}=await this.sendSummaryExchange();this.update({name:e})});o(this,"checkAndGenerateAgentTitle",()=>{var t;if(!(!W(this)||!this._chatFlagModel.summaryTitles||this.name)){var e;!this.name&&(e=this.chatHistory,e.filter(s=>de(s))).length===1&&!((t=this.extraData)!=null&&t.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});o(this,"sendSummaryExchange",()=>{const e={status:_.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:O.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(e)});o(this,"generateCommitMessage",async()=>{let e=`${X}-${crypto.randomUUID()}`;const t={status:_.sent,request_id:e,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:E.unseen,chatItemType:O.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(t);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,e,!0))return;e=s.request_id||e}});o(this,"sendExchange",async(e,t=!1,s)=>{var y;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let a=`${X}-${crypto.randomUUID()}`,r=this._chatFlagModel.isModelIdValid(e.model_id)?e.model_id:void 0;if(R.isNew(this._state)){const d=crypto.randomUUID(),l=this._state.id;try{await this._extensionClient.migrateConversationId(l,d)}catch(g){console.error("Failed to migrate conversation checkpoints:",g)}this._state={...this._state,id:d},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(d),this._subscribers.forEach(g=>g(this))}e=Me(e);let i={status:_.sent,request_id:a,request_message:e.request_message,rich_text_json_repr:e.rich_text_json_repr,model_id:r,mentioned_items:e.mentioned_items,structured_output_nodes:e.structured_output_nodes,seen_state:E.unseen,chatItemType:e.chatItemType,disableSelectedCodeDetails:e.disableSelectedCodeDetails,chatHistory:e.chatHistory,structured_request_nodes:e.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(i),this._loadContextFromExchange(i),this._onSendExchangeListeners.forEach(d=>d(i)),this._chatFlagModel.useHistorySummary&&!e.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),i=await this._addIdeStateNode(i),this.updateExchangeById({structured_request_nodes:i.structured_request_nodes},a,!1);const c=Date.now();let u=!1;for await(const d of this.sendUserMessage(a,i,t,s)){if(((y=this.exchangeWithRequestId(a))==null?void 0:y.status)!==_.sent||!this.updateExchangeById(d,a,!0))return;if(a=d.request_id||a,!u&&W(this)){const l=Date.now(),g=l-c;this._extensionClient.reportAgentRequestEvent({eventName:oe.firstTokenReceived,conversationId:this.id,requestId:a,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:c,firstTokenReceivedTimestampMs:l,timeToFirstTokenMs:g}}}),u=!0}}const m=Date.now()-c;this._chatHistorySummarizationModel.maybeScheduleSummarization(m)});o(this,"sendSuggestedQuestion",e=>{this.sendExchange({request_message:e,status:_.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(je.chatUseSuggestedQuestion)});o(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});o(this,"recoverExchange",async e=>{var a;if(!e.request_id||e.status!==_.sent)return;let t=e.request_id;const s=(a=e.structured_output_nodes)==null?void 0:a.filter(r=>r.type===v.AGENT_MEMORY);this.updateExchangeById({...e,response_text:e.lastChunkId?e.response_text:"",structured_output_nodes:e.lastChunkId?e.structured_output_nodes??[]:s},t);for await(const r of this.getChatStream(e)){if(!this.updateExchangeById(r,t,!0))return;t=r.request_id||t}});o(this,"_loadContextFromConversation",e=>{e.chatHistory.forEach(t=>{T(t)&&this._loadContextFromExchange(t)})});o(this,"_loadContextFromExchange",e=>{e.mentioned_items&&(this._specialContextInputModel.updateItems(e.mentioned_items,[]),this._specialContextInputModel.markItemsActive(e.mentioned_items))});o(this,"_unloadContextFromConversation",e=>{e.chatHistory.forEach(t=>{T(t)&&this._unloadContextFromExchange(t)})});o(this,"_unloadContextFromExchange",e=>{e.mentioned_items&&this._specialContextInputModel.updateItems([],e.mentioned_items)});o(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});o(this,"_jsonToStructuredRequest",e=>{const t=[],s=r=>{var c;const i=t.at(-1);if((i==null?void 0:i.type)===I.TEXT){const u=((c=i.text_node)==null?void 0:c.content)??"",m={...i,text_node:{content:u+r}};t[t.length-1]=m}else t.push({id:t.length,type:I.TEXT,text_node:{content:r}})},a=r=>{var i,c,u,m,y;if(r.type==="doc"||r.type==="paragraph")for(const d of r.content??[])a(d);else if(r.type==="hardBreak")s(`
`);else if(r.type==="text")s(r.text??"");else if(r.type==="file"){if(typeof((i=r.attrs)==null?void 0:i.src)!="string")return void console.error("File source is not a string: ",(c=r.attrs)==null?void 0:c.src);if(r.attrs.isLoading)return;const d=(u=r.attrs)==null?void 0:u.title,l=Ze(d);et(d)?t.push({id:t.length,type:I.IMAGE_ID,image_id_node:{image_id:r.attrs.src,format:l}}):t.push({id:t.length,type:I.FILE_ID,file_id_node:{file_id:r.attrs.src,file_name:d}})}else if(r.type==="mention"){const d=(m=r.attrs)==null?void 0:m.data;d&&Ae(d)?t.push({id:t.length,type:I.TEXT,text_node:{content:Ht(this._chatFlagModel,d.personality.type)}}):d&&wt(d)?t.push({id:t.length,type:I.TEXT,text_node:{content:We.getTaskOrchestratorPrompt(d.task)}}):s(`@\`${(d==null?void 0:d.name)??(d==null?void 0:d.id)}\``)}else if(r.type==="askMode"){const d=(y=r.attrs)==null?void 0:y.prompt;d&&t.push({id:t.length,type:I.TEXT,text_node:{content:d}})}};return a(e),t});this._extensionClient=e,this._chatFlagModel=t,this._specialContextInputModel=s,this._saveConversation=a,this._state={...R.create()},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new Ot(this,e,t)}get conversationId(){return this._state.id}insertChatItem(e,t){const s=[...this._state.chatHistory];s.splice(e,0,t),this.update({chatHistory:s})}_createTotalCharactersStore(){return ft(()=>{let e=0;const t=this._state.chatHistory;return this.convertHistoryToExchanges(t).forEach(s=>{e+=JSON.stringify(s).length}),this._state.draftExchange&&(e+=JSON.stringify(this._state.draftExchange).length),e},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var e;try{return(((e=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:e.reduce((s,a)=>s+a,0))||0)<=4?C.PROTOTYPER:C.DEFAULT}catch(t){return console.error("Error determining persona type:",t),C.DEFAULT}}static create(e={}){const t=new Date().toISOString();return{id:e.id||crypto.randomUUID(),name:void 0,createdAtIso:t,lastInteractedAtIso:t,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:C.DEFAULT,...e}}static toSentenceCase(e){return e.charAt(0).toUpperCase()+e.slice(1)}static getDisplayName(e){if(e.name)return e.name;const t=e.chatHistory.find(T);return t&&t.request_message?R.toSentenceCase(t.request_message):W(e)?"New Agent":"New Chat"}static isNew(e){return e.id===kt}static isEmpty(e){var a;const t=e.chatHistory.filter(r=>T(r)),s=e.chatHistory.filter(r=>Lt(r));return t.length===0&&s.length===0&&!((a=e.draftExchange)!=null&&a.request_message)}static isNamed(e){return e.name!==void 0&&e.name!==""}static getTime(e,t){return t==="lastMessageTimestamp"?R.lastMessageTimestamp(e):t==="lastInteractedAt"?R.lastInteractedAt(e):R.createdAt(e)}static createdAt(e){return new Date(e.createdAtIso)}static lastInteractedAt(e){return new Date(e.lastInteractedAtIso)}static lastMessageTimestamp(e){var s;const t=(s=e.chatHistory.findLast(T))==null?void 0:s.timestamp;return t?new Date(t):this.createdAt(e)}static isValid(e){return e.id!==void 0&&(!R.isEmpty(e)||R.isNamed(e))}onBeforeChangeConversation(e){return this._onBeforeChangeConversationListeners.push(e),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(t=>t!==e)}}_notifyBeforeChangeConversation(e,t){let s=t;for(const a of this._onBeforeChangeConversationListeners){const r=a(e,s);r!==void 0&&(s=r)}return s}get extraData(){return this._state.extraData}set extraData(e){this.update({extraData:e})}get focusModel(){return this._focusModel}get isValid(){return R.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??C.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(e){this.update({rootTaskUuid:e})}get displayName(){return R.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return R.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(e){this.addExchange(e)}get requestIds(){return this._state.chatHistory.map(e=>e.request_id).filter(e=>e!==void 0)}get hasDraft(){var s;const e=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",t=this.hasImagesInDraft();return e||t}hasImagesInDraft(){var s;const e=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!e)return!1;const t=a=>Array.isArray(a)?a.some(t):!!a&&(a.type==="file"||!(!a.content||!Array.isArray(a.content))&&a.content.some(t));return t(e)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(T)??null}get lastExchange(){return this.chatHistory.findLast(T)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(e=>T(e)&&e.status===_.sent)}get successfulMessages(){return this._state.chatHistory.filter(e=>ae(e)||G(e)||P(e))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(e){if(e.length===0)return[];e=this._chatHistorySummarizationModel.preprocessChatHistory(e);const t=[];for(const s of e)if(ae(s))t.push(Ee(s));else if(P(s))t.push(Ee(s));else if(G(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const a=Pt(s,1),r={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[a],response_nodes:[]};t.push(r)}return t}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===_.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(e){const t=crypto.randomUUID();let s,a="";const r=await this._addIdeStateNode(Me({...e,request_id:t,status:_.sent,timestamp:new Date().toISOString()}));for await(const i of this.sendUserMessage(t,r,!0))i.response_text&&(a+=i.response_text),i.request_id&&(s=i.request_id);return{responseText:a,requestId:s}}async*getChatStream(e){e.request_id&&(yield*this._extensionClient.getExistingChatStream(e.request_id,e.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(e,t,s){return[]}_resolveUnresolvedToolUses(e,t,s){var y,d,l;if(e.length===0)return[e,t];const a=e[e.length-1],r=((y=a.response_nodes)==null?void 0:y.filter(g=>g.type===v.TOOL_USE))??[];if(r.length===0)return[e,t];const i=new Set;(d=t.structured_request_nodes)==null||d.forEach(g=>{var p;g.type===I.TOOL_RESULT&&((p=g.tool_result_node)!=null&&p.tool_use_id)&&i.add(g.tool_result_node.tool_use_id)});const c=r.filter(g=>{var x;const p=(x=g.tool_use)==null?void 0:x.tool_use_id;return p&&!i.has(p)});if(c.length===0)return[e,t];const u=c.map((g,p)=>{const x=g.tool_use.tool_use_id;return function(f,q,F,N){const z=bt(q,f,N);let B;if(z!==void 0)B=z;else{let b;switch(q.phase){case S.runnable:b="Tool was cancelled before running.";break;case S.new:b="Cancelled by user.";break;case S.checkingSafety:b="Tool was cancelled during safety check.";break;case S.running:b="Tool was cancelled while running.";break;case S.cancelling:b="Tool cancellation was interrupted.";break;case S.cancelled:b="Cancelled by user.";break;case S.error:b="Tool execution failed.";break;case S.completed:b="Tool completed but result was unavailable.";break;case S.unknown:default:b="Cancelled by user.",q.phase!==S.unknown&&console.error(`Unexpected tool state phase: ${q.phase}`)}B={tool_use_id:f,content:b,is_error:!0}}return{id:F,type:I.TOOL_RESULT,tool_result_node:B}}(x,this.getToolUseState(a.request_id,x),ue(t.structured_request_nodes??[])+p+1,this._chatFlagModel.enableDebugFeatures)});if((l=t.structured_request_nodes)==null?void 0:l.some(g=>g.type===I.TOOL_RESULT))return[e,{...t,structured_request_nodes:[...t.structured_request_nodes??[],...u]}];{const g={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:u,structured_output_nodes:[],status:_.success,hidden:!0};return s||this.addExchangeBeforeLast(g),[e.concat(this.convertHistoryToExchanges([g])),t]}}async*sendUserMessage(e,t,s,a){var d;const r=this._specialContextInputModel.chatActiveContext;let i;if(t.chatHistory!==void 0)i=t.chatHistory;else{let l=this.successfulMessages;if(t.chatItemType===O.summaryTitle){const g=l.findIndex(p=>p.chatItemType!==O.agentOnboarding&&de(p));g!==-1&&(l=l.slice(g))}i=this.convertHistoryToExchanges(l)}this._chatFlagModel.enableParallelTools&&([i,t]=this._resolveUnresolvedToolUses(i,t,s));let c=this.personaType;if(t.structured_request_nodes){const l=t.structured_request_nodes.find(g=>g.type===I.CHANGE_PERSONALITY);l&&l.change_personality_node&&(c=l.change_personality_node.personality_type)}const u={text:t.request_message,chatHistory:i,silent:s,modelId:t.model_id,context:r,userSpecifiedFiles:r.userSpecifiedFiles,externalSourceIds:(d=r.externalSources)==null?void 0:d.map(l=>l.id),disableRetrieval:t.disableRetrieval??!1,disableSelectedCodeDetails:t.disableSelectedCodeDetails??!1,nodes:t.structured_request_nodes,memoriesInfo:t.memoriesInfo,personaType:c,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:a},m=this._createStreamStateHandlers(e,u,{flags:this._chatFlagModel}),y=this._extensionClient.startChatStreamWithRetry(e,u,{flags:this._chatFlagModel});for await(const l of y){let g=l;e=l.request_id||e;for(const p of m)g=p.handleChunk(g)??g;yield g}for(const l of m)yield*l.handleComplete();this.updateExchangeById({structured_request_nodes:t.structured_request_nodes},e)}onSendExchange(e){return this._onSendExchangeListeners.push(e),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(t=>t!==e)}}onNewConversation(e){return this._onNewConversationListeners.push(e),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(t=>t!==e)}}onHistoryDelete(e){return this._onHistoryDeleteListeners.push(e),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(t=>t!==e)}}updateChatItem(e,t){return this.chatHistory.find(s=>s.request_id===e)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===e?{...s,...t}:s)}),!0)}async _addIdeStateNode(e){let t,s=(e.structured_request_nodes??[]).filter(a=>a.type!==I.IDE_STATE);try{t=await this._extensionClient.getChatRequestIdeState()}catch(a){console.error("Failed to add IDE state to exchange:",a)}return t?(s=[...s,{id:ue(s)+1,type:I.IDE_STATE,ide_state_node:t}],{...e,structured_request_nodes:s}):e}}function Pt(n,e){const t=(G(n),n.fromTimestamp),s=(G(n),n.toTimestamp),a=G(n)&&n.revertTarget!==void 0;return{id:e,type:I.CHECKPOINT_REF,checkpoint_ref_node:{request_id:n.request_id||"",from_timestamp:t,to_timestamp:s,source:a?Ke.CHECKPOINT_REVERT:void 0}}}function Ee(n){const e=(n.structured_output_nodes??[]).filter(t=>t.type===v.RAW_RESPONSE||t.type===v.TOOL_USE||t.type===v.TOOL_USE_START).map(t=>t.type===v.TOOL_USE_START?{...t,tool_use:{...t.tool_use,input_json:"{}"},type:v.TOOL_USE}:t);return{request_message:n.request_message,response_text:n.response_text??"",request_id:n.request_id||"",request_nodes:n.structured_request_nodes??[],response_nodes:e}}function ue(n){return n.length>0?Math.max(...n.map(e=>e.id)):0}function Me(n){var e;if(n.request_message.length>0&&!((e=n.structured_request_nodes)!=null&&e.some(t=>t.type===I.TEXT))){let t=n.structured_request_nodes??[];return t=[...t,{id:ue(t)+1,type:I.TEXT,text_node:{content:n.request_message}}],{...n,structured_request_nodes:t}}return n}const Ss="augment-welcome";var _=(n=>(n.draft="draft",n.sent="sent",n.failed="failed",n.success="success",n.cancelled="cancelled",n))(_||{}),D=(n=>(n.running="running",n.awaitingUserAction="awaiting-user-action",n.notRunning="not-running",n))(D||{}),E=(n=>(n.seen="seen",n.unseen="unseen",n))(E||{}),O=(n=>(n.signInWelcome="sign-in-welcome",n.generateCommitMessage="generate-commit-message",n.summaryResponse="summary-response",n.summaryTitle="summary-title",n.educateFeatures="educate-features",n.agentOnboarding="agent-onboarding",n.agenticTurnDelimiter="agentic-turn-delimiter",n.agenticRevertDelimiter="agentic-revert-delimiter",n.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",n.exchange="exchange",n.exchangePointer="exchange-pointer",n.historySummary="history-summary",n))(O||{});function qe(n){return T(n)||Nt(n)||zt(n)}function T(n){return!!n&&(n.chatItemType===void 0||n.chatItemType==="agent-onboarding")}function ae(n){return T(n)&&n.status==="success"}function Lt(n){return!!n&&n.chatItemType==="exchange-pointer"}function vs(n){return n.chatItemType==="sign-in-welcome"}function Nt(n){return n.chatItemType==="generate-commit-message"}function Ts(n){return n.chatItemType==="summary-response"}function xs(n){return n.chatItemType==="educate-features"}function zt(n){return n.chatItemType==="agent-onboarding"}function Is(n){return n.chatItemType==="agentic-turn-delimiter"}function G(n){return n.chatItemType==="agentic-checkpoint-delimiter"}function P(n){return n.chatItemType==="history-summary"}function Cs(n){return n.revertTarget!==void 0}function ws(n,e){const t=function(a){if(!a)return;const r=a.findLast(i=>qe(i.turn));return r?r.turn:void 0}(n);if(!((t==null?void 0:t.status)==="success"||(t==null?void 0:t.status)==="failed"||(t==null?void 0:t.status)==="cancelled"))return!1;const s=function(a){return a?a.findLast(i=>{var c;return!((c=i.turn.request_id)!=null&&c.startsWith(X))&&qe(i.turn)}):void 0}(n);return(s==null?void 0:s.turn.request_id)===e.request_id}function Es(n){var e;return((e=n.structured_output_nodes)==null?void 0:e.some(t=>t.type===v.TOOL_USE))??!1}function Ms(n){var e;return((e=n.structured_request_nodes)==null?void 0:e.some(t=>t.type===I.TOOL_RESULT))??!1}function qs(n){return!(!n||typeof n!="object")&&(!("request_id"in n)||typeof n.request_id=="string")&&(!("seen_state"in n)||n.seen_state==="seen"||n.seen_state==="unseen")}function bs(n){return(n==null?void 0:n.status)==="success"||(n==null?void 0:n.status)==="failed"||(n==null?void 0:n.status)==="cancelled"}function Rs(n){if(!n)return;const e=n.filter(t=>T(t.turn)).map(t=>{return"response_text"in(s=t.turn)?s.response_text??"":"";var s}).filter(t=>t.length>0);return e.length>0?e.join(`
`):void 0}async function*Bt(n,e=1e3){for(;n>0;)yield n,await new Promise(t=>setTimeout(t,Math.min(e,n))),n-=e}class jt{constructor(e,t,s,a=5,r=4e3,i){o(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=t,this.startStreamFn=s,this.maxRetries=a,this.baseDelay=r,this.flags=i}cancel(){this._isCancelled=!0}async*getStream(){let e=0,t=0,s=!1;try{for(;!this._isCancelled;){const a=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let r,i,c=!1,u=!0;for await(const m of a){if(m.status===_.failed){if(m.isRetriable!==!0||s)return yield m;c=!0,u=m.shouldBackoff??!0,r=m.display_error_message,i=m.request_id;break}s=!0,yield m}if(!c)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${r}`),void(yield{request_id:i??this.requestId,seen_state:E.unseen,status:_.failed,display_error_message:r,isRetriable:!1});if(u){const m=this.baseDelay*2**t;t++;for await(const y of Bt(m))yield{request_id:this.requestId,status:_.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(y/1e3)} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:_.sent,display_error_message:`Generating response... (Attempt ${e+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(a){console.error("Unexpected error in chat stream:",a),yield{request_id:this.requestId,seen_state:E.unseen,status:_.failed,display_error_message:a instanceof Error?a.message:String(a)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:E.unseen,status:_.cancelled}}}class Wt{constructor(e){o(this,"getHydratedTask",async e=>{const t={type:j.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.task});o(this,"createTask",async(e,t,s)=>{const a={type:j.createTaskRequest,data:{name:e,description:t,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.uuid});o(this,"updateTask",async(e,t,s)=>{const a={type:j.updateTaskRequest,data:{uuid:e,updates:t,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(a,3e4)});o(this,"setCurrentRootTaskUuid",e=>{const t={type:j.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(t)});o(this,"updateHydratedTask",async(e,t)=>{const s={type:j.updateHydratedTaskRequest,data:{task:e,updatedBy:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=e}}class As{constructor(e,t,s){o(this,"_taskClient");o(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:h.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const t=await async function(s){return(await _t(Ye,new Ve({sendMessage:r=>{s.postMessage(r)},onReceiveMessage:r=>{const i=c=>{r(c.data)};return window.addEventListener("message",i),()=>{window.removeEventListener("message",i)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",t)}catch(t){console.error("Hello world error:",t)}return e.data});o(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:h.reportWebviewClientMetric,data:{webviewName:$e.chat,client_metric:e,value:1}})});o(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:w.reportAgentSessionEvent,data:e})});o(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:w.reportAgentRequestEvent,data:e})});o(this,"getSuggestions",async(e,t=!1)=>{const s={rootPath:"",relPath:e},a=this.findFiles(s,6),r=this.findRecentlyOpenedFiles(s,6),i=this.findFolders(s,3),c=this.findExternalSources(e,t),u=this._flags.enableRules?this.findRules(e,6):Promise.resolve([]),[m,y,d,l,g]=await Promise.all([$(a,[]),$(r,[]),$(i,[]),$(c,[]),$(u,[])]),p=(f,q)=>({...Et(f),[q]:f}),x=[...m.map(f=>p(f,"file")),...d.map(f=>p(f,"folder")),...y.map(f=>p(f,"recentFile")),...l.map(f=>({label:f.name,name:f.name,id:f.id,externalSource:f})),...g.map(f=>({...Mt(f),rule:f}))];if(this._flags.enablePersonalities){const f=this.getPersonalities(e);f.length>0&&x.push(...f)}return x});o(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return Ce;const t=e.toLowerCase();return Ce.filter(s=>{const a=s.personality.description.toLowerCase(),r=s.label.toLowerCase();return a.includes(t)||r.includes(t)})});o(this,"sendAction",e=>{this._host.postMessage({type:h.mainPanelPerformAction,data:e})});o(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:h.showAugmentPanel})});o(this,"showNotification",e=>{this._host.postMessage({type:h.showNotification,data:e})});o(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:h.openConfirmationModal,data:e},1e9)).data.ok);o(this,"clearMetadataFor",e=>{this._host.postMessage({type:h.chatClearMetadata,data:e})});o(this,"resolvePath",async(e,t=void 0)=>{const s=await this._asyncMsgSender.send({type:h.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:t}},5e3);if(s.data)return s.data});o(this,"resolveSymbols",async(e,t)=>(await this._asyncMsgSender.send({type:h.findSymbolRequest,data:{query:e,searchScope:t}},3e4)).data);o(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:h.getDiagnosticsRequest},1e3)).data);o(this,"findFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:h.findFileRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findFolders",async(e,t=12)=>(await this._asyncMsgSender.send({type:h.findFolderRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findRecentlyOpenedFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:h.findRecentlyOpenedFilesRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findExternalSources",async(e,t=!1)=>this._flags.enableExternalSourcesInChat?t?[]:(await this._asyncMsgSender.send({type:h.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);o(this,"findRules",async(e,t=12)=>(await this._asyncMsgSender.sendToSidecar({type:Ge.getRulesListRequest,data:{query:e,maxResults:t}})).data.rules);o(this,"openFile",e=>{this._host.postMessage({type:h.openFile,data:e})});o(this,"saveFile",e=>this._host.postMessage({type:h.saveFile,data:e}));o(this,"loadFile",e=>this._host.postMessage({type:h.loadFile,data:e}));o(this,"openMemoriesFile",()=>{this._host.postMessage({type:h.openMemoriesFile})});o(this,"canShowTerminal",async(e,t)=>{try{return(await this._asyncMsgSender.send({type:h.canShowTerminal,data:{terminalId:e,command:t}},5e3)).data.canShow}catch(s){return console.error("Failed to check if terminal can be shown:",s),!1}});o(this,"showTerminal",async(e,t)=>{try{return(await this._asyncMsgSender.send({type:h.showTerminal,data:{terminalId:e,command:t}},5e3)).data.success}catch(s){return console.error("Failed to show terminal:",s),!1}});o(this,"createFile",(e,t)=>{this._host.postMessage({type:h.chatCreateFile,data:{code:e,relPath:t}})});o(this,"openScratchFile",async(e,t="shellscript")=>{await this._asyncMsgSender.send({type:h.openScratchFileRequest,data:{content:e,language:t}},1e4)});o(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:h.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});o(this,"smartPaste",e=>{this._host.postMessage({type:h.chatSmartPaste,data:e})});o(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));o(this,"updateHydratedTask",async(e,t)=>this._taskClient.updateHydratedTask(e,t));o(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});o(this,"createTask",async(e,t,s)=>this._taskClient.createTask(e,t,s));o(this,"updateTask",async(e,t,s)=>this._taskClient.updateTask(e,t,s));o(this,"saveChat",async(e,t,s)=>this._asyncMsgSender.send({type:h.saveChat,data:{conversationId:e,chatHistory:t,title:s}},5e3));o(this,"updateUserGuidelines",e=>{this._host.postMessage({type:h.updateUserGuidelines,data:e})});o(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:h.updateWorkspaceGuidelines,data:e})});o(this,"openSettingsPage",e=>{this._host.postMessage({type:h.openSettingsPage,data:e})});o(this,"_activeRetryStreams",new Map);o(this,"cancelChatStream",async e=>{var t;(t=this._activeRetryStreams.get(e))==null||t.cancel(),await this._asyncMsgSender.send({type:h.chatUserCancel,data:{requestId:e}},1e4)});o(this,"sendUserRating",async(e,t,s,a="")=>{const r={requestId:e,rating:s,note:a,mode:t},i={type:h.chatRating,data:r};return(await this._asyncMsgSender.send(i,3e4)).data});o(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:h.usedChat})});o(this,"createProject",e=>{this._host.postMessage({type:h.mainPanelCreateProject,data:{name:e}})});o(this,"openProjectFolder",()=>{this._host.postMessage({type:h.mainPanelPerformAction,data:"open-folder"})});o(this,"closeProjectFolder",()=>{this._host.postMessage({type:h.mainPanelPerformAction,data:"close-folder"})});o(this,"cloneRepository",()=>{this._host.postMessage({type:h.mainPanelPerformAction,data:"clone-repository"})});o(this,"grantSyncPermission",()=>{this._host.postMessage({type:h.mainPanelPerformAction,data:"grant-sync-permission"})});o(this,"startRemoteMCPAuth",e=>{this._host.postMessage({type:h.startRemoteMCPAuth,data:{name:e}})});o(this,"callTool",async(e,t,s,a,r,i)=>{const c={type:h.callTool,data:{chatRequestId:e,toolUseId:t,name:s,input:a,chatHistory:r,conversationId:i}};return(await this._asyncMsgSender.send(c,0)).data});o(this,"cancelToolRun",async(e,t)=>{const s={type:h.cancelToolRun,data:{requestId:e,toolUseId:t}};await this._asyncMsgSender.send(s,0)});o(this,"checkSafe",async e=>{const t={type:Q.checkToolCallSafeRequest,data:e};return(await this._asyncMsgSender.sendToSidecar(t,0)).data});o(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:Q.closeAllToolProcesses},0)});o(this,"getToolIdentifier",async e=>{const t={type:Q.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(t,0)).data});o(this,"getChatMode",async()=>{const e={type:w.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(e,3e4)).data.chatMode});o(this,"setChatMode",e=>{this._asyncMsgSender.send({type:h.chatModeChanged,data:{mode:e}})});o(this,"getAgentEditList",async(e,t)=>{const s={type:w.getEditListRequest,data:{fromTimestamp:e,toTimestamp:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"hasChangesSince",async e=>{const t={type:w.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.edits.filter(s=>{var a,r;return((a=s.changesSummary)==null?void 0:a.totalAddedLines)||((r=s.changesSummary)==null?void 0:r.totalRemovedLines)}).length>0});o(this,"getToolCallCheckpoint",async e=>{const t={type:h.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(t,3e4)).data.checkpointNumber});o(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:w.setCurrentConversation,data:{conversationId:e}})});o(this,"migrateConversationId",async(e,t)=>{await this._asyncMsgSender.sendToSidecar({type:w.migrateConversationId,data:{oldConversationId:e,newConversationId:t}},3e4)});o(this,"showAgentReview",(e,t,s,a=!0,r)=>{this._asyncMsgSender.sendToSidecar({type:w.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:t,toTimestamp:s,retainFocus:a,useNativeDiffIfAvailable:r}})});o(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:w.chatAgentEditAcceptAll}),!0));o(this,"revertToTimestamp",async(e,t)=>(await this._asyncMsgSender.sendToSidecar({type:w.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:t}}),!0));o(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:h.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);o(this,"getAgentEditChangesByRequestId",async e=>{const t={type:w.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});o(this,"getAgentEditContentsByRequestId",async e=>{const t={type:w.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});o(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:h.triggerInitialOrientation})});o(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:h.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});o(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:h.toggleCollapseUnchangedRegions})});o(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:h.checkAgentAutoModeApproval},5e3)).data);o(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:h.setAgentAutoModeApproved,data:e},5e3)});o(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:w.checkHasEverUsedAgent},5e3)).data);o(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:w.setHasEverUsedAgent,data:e},5e3)});o(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:w.checkHasEverUsedRemoteAgent},5e3)).data);o(this,"setHasEverUsedRemoteAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:w.setHasEverUsedRemoteAgent,data:e},5e3)});o(this,"getChatRequestIdeState",async()=>{const e={type:h.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});o(this,"reportError",e=>{this._host.postMessage({type:h.reportError,data:e})});o(this,"sendMemoryCreated",async e=>{await this._asyncMsgSender.sendToSidecar(e,5e3)});o(this,"sendGitMessage",async e=>await this._asyncMsgSender.sendToSidecar(e,3e4));this._host=e,this._asyncMsgSender=t,this._flags=s,this._taskClient=new Wt(t)}async*generateCommitMessage(){const e={type:h.generateCommitMessage},t=this._asyncMsgSender.stream(e,3e4,6e4);yield*re(t,()=>{},this._flags.retryChatStreamTimeouts)}async*sendInstructionMessage(e,t){const s={instruction:e.request_message??"",selectedCodeDetails:t,requestId:e.request_id},a={type:h.chatInstructionMessage,data:s},r=this._asyncMsgSender.stream(a,3e4,6e4);yield*async function*(i){let c;try{for await(const u of i)c=u.data.requestId,yield{request_id:c,response_text:u.data.text,seen_state:E.unseen,status:_.sent};yield{request_id:c,seen_state:E.unseen,status:_.success}}catch(u){console.error("Error in chat instruction model reply stream:",u),yield{request_id:c,seen_state:E.unseen,status:_.failed}}}(r)}async openGuidelines(e){this._host.postMessage({type:h.openGuidelines,data:e})}async*getExistingChatStream(e,t,s){const a=s==null?void 0:s.flags.enablePreferenceCollection,r=a?1e9:6e4,i=a?1e9:3e5,c={type:h.chatGetStreamRequest,data:{requestId:e,lastChunkId:t}},u=this._asyncMsgSender.stream(c,r,i);yield*re(u,this.reportError,this._flags.retryChatStreamTimeouts)}async*startChatStream(e,t){const s=t==null?void 0:t.flags.enablePreferenceCollection,a=s?1e9:1e5,r=s?1e9:3e5,i={type:h.chatUserMessage,data:e},c=this._asyncMsgSender.stream(i,a,r);yield*re(c,this.reportError,this._flags.retryChatStreamTimeouts)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:h.checkToolExists,toolName:e},0)).exists}async saveImage(e,t){const s=pe(await Z(e)),a=t??`${await ye(await ee(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:h.chatSaveImageRequest,data:{filename:a,data:s}},1e4)).data}async saveAttachment(e,t){const s=pe(await Z(e)),a=t??`${await ye(await ee(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:h.chatSaveAttachmentRequest,data:{filename:a,data:s}},1e4)).data}async loadImage(e){const t=await this._asyncMsgSender.send({type:h.chatLoadImageRequest,data:e},1e4),s=t.data?await ee(t.data):void 0;if(!s)return;let a="application/octet-stream";const r=e.split(".").at(-1);r==="png"?a="image/png":r!=="jpg"&&r!=="jpeg"||(a="image/jpeg");const i=new File([s],e,{type:a});return await Z(i)}async deleteImage(e){await this._asyncMsgSender.send({type:h.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,t,s){const a=new jt(e,t,(r,i)=>this.startChatStream(r,i),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(e,a);try{yield*a.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:h.getSubscriptionInfo},5e3)}async loadExchanges(e,t){if(t.length===0)return[];const s={type:te.loadExchangesByUuidsRequest,data:{conversationId:e,uuids:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.exchanges}async saveExchanges(e,t){if(t.length===0)return;const s={type:te.saveExchangesRequest,data:{conversationId:e,exchanges:t}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationExchanges(e){const t={type:te.deleteConversationExchangesRequest,data:{conversationId:e}};await this._asyncMsgSender.sendToSidecar(t,3e4)}async loadConversationToolUseStates(e){const t={type:se.loadConversationToolUseStatesRequest,data:{conversationId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.toolUseStates}async saveToolUseStates(e,t){if(Object.keys(t).length===0)return;const s={type:se.saveToolUseStatesRequest,data:{conversationId:e,toolUseStates:t}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationToolUseStates(e){const t={type:se.deleteConversationToolUseStatesRequest,data:{conversationId:e}};await this._asyncMsgSender.sendToSidecar(t,3e4)}}async function*re(n,e=()=>{},t){let s;try{for await(const a of n){if(s=a.data.requestId,a.data.error)return console.error("Error in chat model reply stream:",a.data.error.displayErrorMessage),yield{request_id:s,seen_state:E.unseen,status:_.failed,display_error_message:a.data.error.displayErrorMessage,isRetriable:a.data.error.isRetriable,shouldBackoff:a.data.error.shouldBackoff};const r={request_id:s,response_text:a.data.text,workspace_file_chunks:a.data.workspaceFileChunks,structured_output_nodes:$t(a.data.nodes),seen_state:E.unseen,status:_.sent,lastChunkId:a.data.chunkId};a.data.stop_reason!=null&&(r.stop_reason=a.data.stop_reason),yield r}yield{request_id:s,seen_state:E.unseen,status:_.success}}catch(a){let r,i;if(e({originalRequestId:s||"",sanitizedMessage:a instanceof Error?a.message:String(a),stackTrace:a instanceof Error&&a.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),a instanceof Xe&&t)switch(a.name){case"MessageTimeout":r=!0,i=!1;break;case"StreamTimeout":case"InvalidResponse":r=!1}console.error("Unexpected error in chat model reply stream:",a),yield{request_id:s,seen_state:E.unseen,status:_.failed,isRetriable:r,shouldBackoff:i}}}async function $(n,e){try{return await n}catch(t){return console.warn(`Error while resolving promise: ${t}`),e}}function $t(n){if(!n)return n;let e=!1;return n.filter(t=>t.type!==v.TOOL_USE||!e&&(e=!0,!0))}function Gt(n){let e,t,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],a={};for(let r=0;r<s.length;r+=1)a=ce(a,s[r]);return{c(){e=rt("svg"),t=new it(!0),this.h()},l(r){e=ot(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=ct(e);t=dt(i,!0),i.forEach(_e),this.h()},h(){t.a=null,fe(e,a)},m(r,i){ut(r,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 401c9.4 9.4 24.6 9.4 33.9 0L465 209c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 175c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"/>',e)},p(r,[i]){fe(e,a=lt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&r[0]]))},i:Se,o:Se,d(r){r&&_e(e)}}}function Vt(n,e,t){return n.$$set=s=>{t(0,e=ce(ce({},e),ve(s)))},[e=ve(e)]}class ks extends st{constructor(e){super(),nt(this,e,Vt,Gt,at,{})}}export{Ss as $,D as A,os as B,ks as C,Dt as D,As as E,gs as F,hs as G,as as H,ds as I,us as J,rs as K,vs as L,Nt as M,kt as N,xs as O,zt as P,G as Q,Es as R,E as S,Ms as T,cs as U,ws as V,Cs as W,X,qs as Y,qe as Z,Lt as _,_ as a,ls as a0,Et as a1,Mt as a2,ps as a3,_s as a4,At as a5,Re as a6,Rs as a7,bs as a8,O as b,W as c,ys as d,R as e,ae as f,Ut as g,Ee as h,Ts as i,T as j,bt as k,ms as l,fs as m,Is as n,St as o,vt as p,Tt as q,It as r,xt as s,Rt as t,is as u,ns as v,Ae as w,wt as x,Ct as y,ss as z};
