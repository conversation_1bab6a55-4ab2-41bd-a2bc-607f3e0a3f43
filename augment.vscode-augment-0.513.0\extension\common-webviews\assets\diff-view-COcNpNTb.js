var So=Object.defineProperty;var kn=r=>{throw TypeError(r)};var xo=(r,t,e)=>t in r?So(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var l=(r,t,e)=>xo(r,typeof t!="symbol"?t+"":t,e),mi=(r,t,e)=>t.has(r)||kn("Cannot "+e);var f=(r,t,e)=>(mi(r,t,"read from private field"),e?e.call(r):t.get(r)),B=(r,t,e)=>t.has(r)?kn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e),R=(r,t,e,s)=>(mi(r,t,"write to private field"),s?s.call(r,e):t.set(r,e),e),A=(r,t,e)=>(mi(r,t,"access private method"),e);var Hs=(r,t,e,s)=>({set _(i){R(r,t,i,e)},get _(){return f(r,t,s)}});import{c as Me,g as $r,C as ut,aw as An,ao as It,ax as Ei,S as ke,i as Ae,s as Fe,ab as de,D as et,V as J,E,d as X,a0 as jt,P as ts,f as O,h as st,F as T,ae as Cr,Q as Ie,w as $,t as gt,u as b,v as _t,k as F,G as k,T as Xi,ak as Ve,Y as ct,a5 as ss,a6 as wo,a7 as Mo,N as ie,n as Z,a4 as he,al as br,Z as Ht,a as Ti,b as Io,H as Eo,y as To,z as ko,A as Ao,e as Fn,B as Fo,j as Oo,l as On,an as pi,at as cs,X as ls,aj as No,ag as Zi,a8 as Sr,am as Lo,ac as xr,ay as Ro,U as Do}from"./SpinnerAugment-uKUHz-bK.js";import"./design-system-init-DDX-Gvwz.js";import{S as qo,W as lt,h as vs,D as gi,I as Uo,e as Ks,g as Ho}from"./IconButtonAugment-CQzh_Hae.js";import{A as Po}from"./async-messaging-D4p6YcQf.js";import{d as ki}from"./index-BBwB6w04.js";import{i as Mt,a as es,b as je,c as ht,S as ue,d as Ai,e as is,f as $s,g as ii,h as zo,j as wr,C as jo,E as Wo,D as Go,k as Vo,l as Bo,s as _i,m as Ji,n as Qs,o as Xs,p as Yi,q as Zs,r as tn,t as Js,A as Ko,u as Ys,v as yi,w as Qo,x as Xo,y as Cs,z as Mr,U as Ir,B as Er,F as Tr,G as Zo}from"./chat-flags-model-BrC28MCB.js";import{f as xt,i as Nn,j as Jo,s as Yo,k as vi,l as Fi,F as kr,m as Ln,n as $i,C as Oi,o as ta,I as ea,J as Rn,D as Ci,b as Dn,c as qn}from"./index-DP6mqmYw.js";import{C as sa}from"./types-CGlLNakm.js";import{C as pt,a as wt,b as bi,I as Ps,P as le,E as ia,M as Ar}from"./message-broker-DdVtH9Vr.js";import{f as na,i as Fr}from"./file-paths-CAgP5Fvb.js";import{c as ra,K as Be,C as oa,F as aa,a as Or}from"./folder-opened-D8PSJjEt.js";import{B as Ee}from"./ButtonAugment-D5QDitBR.js";import{a as ca,T as la}from"./CardAugment-BqjOeIg4.js";import{A as Un,R as ha,B as ua,P as da,T as fa,a as Nr,b as ma,C as pa,c as ga,G as _a,d as ya,M as Ni,e as Lr,K as va,f as $a}from"./Keybindings-DJDYMwQg.js";import{F as Ot}from"./Filespan-DNGY17t7.js";import{M as hs}from"./MaterialIcon-ggitH03G.js";import{a as Ca,M as ba,b as Sa,C as xa}from"./index-D0JCd9Au.js";import"./index-Bcx5x-t6.js";import"./BaseTextInput-BTYl5feP.js";import"./CalloutAugment-CznTrv4g.js";import"./exclamation-triangle-Ba2-vrJ4.js";import"./pen-to-square-BWYRDHTI.js";import"./augment-logo-btpqr34Z.js";var Li={exports:{}};(function(r,t){var e="__lodash_hash_undefined__",s=1,i=2,n=9007199254740991,o="[object Arguments]",a="[object Array]",c="[object AsyncFunction]",h="[object Boolean]",d="[object Date]",u="[object Error]",m="[object Function]",p="[object GeneratorFunction]",C="[object Map]",x="[object Number]",S="[object Null]",y="[object Object]",_="[object Promise]",w="[object Proxy]",N="[object RegExp]",D="[object Set]",j="[object String]",z="[object Symbol]",W="[object Undefined]",P="[object WeakMap]",Y="[object ArrayBuffer]",ot="[object DataView]",Tt=/^\[object .+?Constructor\]$/,K=/^(?:0|[1-9]\d*)$/,G={};G["[object Float32Array]"]=G["[object Float64Array]"]=G["[object Int8Array]"]=G["[object Int16Array]"]=G["[object Int32Array]"]=G["[object Uint8Array]"]=G["[object Uint8ClampedArray]"]=G["[object Uint16Array]"]=G["[object Uint32Array]"]=!0,G[o]=G[a]=G[Y]=G[h]=G[ot]=G[d]=G[u]=G[m]=G[C]=G[x]=G[y]=G[N]=G[D]=G[j]=G[P]=!1;var Pt=typeof Me=="object"&&Me&&Me.Object===Object&&Me,Oe=typeof self=="object"&&self&&self.Object===Object&&self,Nt=Pt||Oe||Function("return this")(),U=t&&!t.nodeType&&t,Ne=U&&r&&!r.nodeType&&r,Ke=Ne&&Ne.exports===U,us=Ke&&Pt.process,xs=function(){try{return us&&us.binding&&us.binding("util")}catch{}}(),on=xs&&xs.isTypedArray;function Yr(g,v){for(var M=-1,L=g==null?0:g.length;++M<L;)if(v(g[M],M,g))return!0;return!1}function to(g){var v=-1,M=Array(g.size);return g.forEach(function(L,it){M[++v]=[it,L]}),M}function eo(g){var v=-1,M=Array(g.size);return g.forEach(function(L){M[++v]=L}),M}var an,cn,ln,so=Array.prototype,io=Function.prototype,ws=Object.prototype,ni=Nt["__core-js_shared__"],hn=io.toString,te=ws.hasOwnProperty,un=(an=/[^.]+$/.exec(ni&&ni.keys&&ni.keys.IE_PROTO||""))?"Symbol(src)_1."+an:"",dn=ws.toString,no=RegExp("^"+hn.call(te).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),fn=Ke?Nt.Buffer:void 0,Ms=Nt.Symbol,mn=Nt.Uint8Array,pn=ws.propertyIsEnumerable,ro=so.splice,Le=Ms?Ms.toStringTag:void 0,gn=Object.getOwnPropertySymbols,oo=fn?fn.isBuffer:void 0,ao=(cn=Object.keys,ln=Object,function(g){return cn(ln(g))}),ri=Qe(Nt,"DataView"),ds=Qe(Nt,"Map"),oi=Qe(Nt,"Promise"),ai=Qe(Nt,"Set"),ci=Qe(Nt,"WeakMap"),fs=Qe(Object,"create"),co=qe(ri),lo=qe(ds),ho=qe(oi),uo=qe(ai),fo=qe(ci),_n=Ms?Ms.prototype:void 0,li=_n?_n.valueOf:void 0;function Re(g){var v=-1,M=g==null?0:g.length;for(this.clear();++v<M;){var L=g[v];this.set(L[0],L[1])}}function ne(g){var v=-1,M=g==null?0:g.length;for(this.clear();++v<M;){var L=g[v];this.set(L[0],L[1])}}function De(g){var v=-1,M=g==null?0:g.length;for(this.clear();++v<M;){var L=g[v];this.set(L[0],L[1])}}function Is(g){var v=-1,M=g==null?0:g.length;for(this.__data__=new De;++v<M;)this.add(g[v])}function me(g){var v=this.__data__=new ne(g);this.size=v.size}function mo(g,v){var M=ks(g),L=!M&&vo(g),it=!M&&!L&&hi(g),V=!M&&!L&&!it&&Mn(g),at=M||L||it||V,dt=at?function(vt,ee){for(var re=-1,kt=Array(vt);++re<vt;)kt[re]=ee(re);return kt}(g.length,String):[],Wt=dt.length;for(var yt in g)!te.call(g,yt)||at&&(yt=="length"||it&&(yt=="offset"||yt=="parent")||V&&(yt=="buffer"||yt=="byteLength"||yt=="byteOffset")||yo(yt,Wt))||dt.push(yt);return dt}function Es(g,v){for(var M=g.length;M--;)if(bn(g[M][0],v))return M;return-1}function ms(g){return g==null?g===void 0?W:S:Le&&Le in Object(g)?function(v){var M=te.call(v,Le),L=v[Le];try{v[Le]=void 0;var it=!0}catch{}var V=dn.call(v);return it&&(M?v[Le]=L:delete v[Le]),V}(g):function(v){return dn.call(v)}(g)}function yn(g){return ps(g)&&ms(g)==o}function vn(g,v,M,L,it){return g===v||(g==null||v==null||!ps(g)&&!ps(v)?g!=g&&v!=v:function(V,at,dt,Wt,yt,vt){var ee=ks(V),re=ks(at),kt=ee?a:pe(V),oe=re?a:pe(at),Xe=(kt=kt==o?y:kt)==y,As=(oe=oe==o?y:oe)==y,Ze=kt==oe;if(Ze&&hi(V)){if(!hi(at))return!1;ee=!0,Xe=!1}if(Ze&&!Xe)return vt||(vt=new me),ee||Mn(V)?$n(V,at,dt,Wt,yt,vt):function(tt,Q,Fs,ge,ui,zt,ae){switch(Fs){case ot:if(tt.byteLength!=Q.byteLength||tt.byteOffset!=Q.byteOffset)return!1;tt=tt.buffer,Q=Q.buffer;case Y:return!(tt.byteLength!=Q.byteLength||!zt(new mn(tt),new mn(Q)));case h:case d:case x:return bn(+tt,+Q);case u:return tt.name==Q.name&&tt.message==Q.message;case N:case j:return tt==Q+"";case C:var _e=to;case D:var _s=ge&s;if(_e||(_e=eo),tt.size!=Q.size&&!_s)return!1;var Os=ae.get(tt);if(Os)return Os==Q;ge|=i,ae.set(tt,Q);var di=$n(_e(tt),_e(Q),ge,ui,zt,ae);return ae.delete(tt),di;case z:if(li)return li.call(tt)==li.call(Q)}return!1}(V,at,kt,dt,Wt,yt,vt);if(!(dt&s)){var gs=Xe&&te.call(V,"__wrapped__"),In=As&&te.call(at,"__wrapped__");if(gs||In){var Co=gs?V.value():V,bo=In?at.value():at;return vt||(vt=new me),yt(Co,bo,dt,Wt,vt)}}return Ze?(vt||(vt=new me),function(tt,Q,Fs,ge,ui,zt){var ae=Fs&s,_e=Cn(tt),_s=_e.length,Os=Cn(Q),di=Os.length;if(_s!=di&&!ae)return!1;for(var Ns=_s;Ns--;){var Ue=_e[Ns];if(!(ae?Ue in Q:te.call(Q,Ue)))return!1}var En=zt.get(tt);if(En&&zt.get(Q))return En==Q;var Ls=!0;zt.set(tt,Q),zt.set(Q,tt);for(var fi=ae;++Ns<_s;){var Rs=tt[Ue=_e[Ns]],Ds=Q[Ue];if(ge)var Tn=ae?ge(Ds,Rs,Ue,Q,tt,zt):ge(Rs,Ds,Ue,tt,Q,zt);if(!(Tn===void 0?Rs===Ds||ui(Rs,Ds,Fs,ge,zt):Tn)){Ls=!1;break}fi||(fi=Ue=="constructor")}if(Ls&&!fi){var qs=tt.constructor,Us=Q.constructor;qs==Us||!("constructor"in tt)||!("constructor"in Q)||typeof qs=="function"&&qs instanceof qs&&typeof Us=="function"&&Us instanceof Us||(Ls=!1)}return zt.delete(tt),zt.delete(Q),Ls}(V,at,dt,Wt,yt,vt)):!1}(g,v,M,L,vn,it))}function po(g){return!(!wn(g)||function(v){return!!un&&un in v}(g))&&(Sn(g)?no:Tt).test(qe(g))}function go(g){if(M=(v=g)&&v.constructor,L=typeof M=="function"&&M.prototype||ws,v!==L)return ao(g);var v,M,L,it=[];for(var V in Object(g))te.call(g,V)&&V!="constructor"&&it.push(V);return it}function $n(g,v,M,L,it,V){var at=M&s,dt=g.length,Wt=v.length;if(dt!=Wt&&!(at&&Wt>dt))return!1;var yt=V.get(g);if(yt&&V.get(v))return yt==v;var vt=-1,ee=!0,re=M&i?new Is:void 0;for(V.set(g,v),V.set(v,g);++vt<dt;){var kt=g[vt],oe=v[vt];if(L)var Xe=at?L(oe,kt,vt,v,g,V):L(kt,oe,vt,g,v,V);if(Xe!==void 0){if(Xe)continue;ee=!1;break}if(re){if(!Yr(v,function(As,Ze){if(gs=Ze,!re.has(gs)&&(kt===As||it(kt,As,M,L,V)))return re.push(Ze);var gs})){ee=!1;break}}else if(kt!==oe&&!it(kt,oe,M,L,V)){ee=!1;break}}return V.delete(g),V.delete(v),ee}function Cn(g){return function(v,M,L){var it=M(v);return ks(v)?it:function(V,at){for(var dt=-1,Wt=at.length,yt=V.length;++dt<Wt;)V[yt+dt]=at[dt];return V}(it,L(v))}(g,$o,_o)}function Ts(g,v){var M,L,it=g.__data__;return((L=typeof(M=v))=="string"||L=="number"||L=="symbol"||L=="boolean"?M!=="__proto__":M===null)?it[typeof v=="string"?"string":"hash"]:it.map}function Qe(g,v){var M=function(L,it){return L==null?void 0:L[it]}(g,v);return po(M)?M:void 0}Re.prototype.clear=function(){this.__data__=fs?fs(null):{},this.size=0},Re.prototype.delete=function(g){var v=this.has(g)&&delete this.__data__[g];return this.size-=v?1:0,v},Re.prototype.get=function(g){var v=this.__data__;if(fs){var M=v[g];return M===e?void 0:M}return te.call(v,g)?v[g]:void 0},Re.prototype.has=function(g){var v=this.__data__;return fs?v[g]!==void 0:te.call(v,g)},Re.prototype.set=function(g,v){var M=this.__data__;return this.size+=this.has(g)?0:1,M[g]=fs&&v===void 0?e:v,this},ne.prototype.clear=function(){this.__data__=[],this.size=0},ne.prototype.delete=function(g){var v=this.__data__,M=Es(v,g);return!(M<0)&&(M==v.length-1?v.pop():ro.call(v,M,1),--this.size,!0)},ne.prototype.get=function(g){var v=this.__data__,M=Es(v,g);return M<0?void 0:v[M][1]},ne.prototype.has=function(g){return Es(this.__data__,g)>-1},ne.prototype.set=function(g,v){var M=this.__data__,L=Es(M,g);return L<0?(++this.size,M.push([g,v])):M[L][1]=v,this},De.prototype.clear=function(){this.size=0,this.__data__={hash:new Re,map:new(ds||ne),string:new Re}},De.prototype.delete=function(g){var v=Ts(this,g).delete(g);return this.size-=v?1:0,v},De.prototype.get=function(g){return Ts(this,g).get(g)},De.prototype.has=function(g){return Ts(this,g).has(g)},De.prototype.set=function(g,v){var M=Ts(this,g),L=M.size;return M.set(g,v),this.size+=M.size==L?0:1,this},Is.prototype.add=Is.prototype.push=function(g){return this.__data__.set(g,e),this},Is.prototype.has=function(g){return this.__data__.has(g)},me.prototype.clear=function(){this.__data__=new ne,this.size=0},me.prototype.delete=function(g){var v=this.__data__,M=v.delete(g);return this.size=v.size,M},me.prototype.get=function(g){return this.__data__.get(g)},me.prototype.has=function(g){return this.__data__.has(g)},me.prototype.set=function(g,v){var M=this.__data__;if(M instanceof ne){var L=M.__data__;if(!ds||L.length<199)return L.push([g,v]),this.size=++M.size,this;M=this.__data__=new De(L)}return M.set(g,v),this.size=M.size,this};var _o=gn?function(g){return g==null?[]:(g=Object(g),function(v,M){for(var L=-1,it=v==null?0:v.length,V=0,at=[];++L<it;){var dt=v[L];M(dt,L,v)&&(at[V++]=dt)}return at}(gn(g),function(v){return pn.call(g,v)}))}:function(){return[]},pe=ms;function yo(g,v){return!!(v=v??n)&&(typeof g=="number"||K.test(g))&&g>-1&&g%1==0&&g<v}function qe(g){if(g!=null){try{return hn.call(g)}catch{}try{return g+""}catch{}}return""}function bn(g,v){return g===v||g!=g&&v!=v}(ri&&pe(new ri(new ArrayBuffer(1)))!=ot||ds&&pe(new ds)!=C||oi&&pe(oi.resolve())!=_||ai&&pe(new ai)!=D||ci&&pe(new ci)!=P)&&(pe=function(g){var v=ms(g),M=v==y?g.constructor:void 0,L=M?qe(M):"";if(L)switch(L){case co:return ot;case lo:return C;case ho:return _;case uo:return D;case fo:return P}return v});var vo=yn(function(){return arguments}())?yn:function(g){return ps(g)&&te.call(g,"callee")&&!pn.call(g,"callee")},ks=Array.isArray,hi=oo||function(){return!1};function Sn(g){if(!wn(g))return!1;var v=ms(g);return v==m||v==p||v==c||v==w}function xn(g){return typeof g=="number"&&g>-1&&g%1==0&&g<=n}function wn(g){var v=typeof g;return g!=null&&(v=="object"||v=="function")}function ps(g){return g!=null&&typeof g=="object"}var Mn=on?function(g){return function(v){return g(v)}}(on):function(g){return ps(g)&&xn(g.length)&&!!G[ms(g)]};function $o(g){return(v=g)!=null&&xn(v.length)&&!Sn(v)?mo(g):go(g);var v}r.exports=function(g,v){return vn(g,v)}})(Li,Li.exports);const wa=$r(Li.exports);function Rr(r){return function(t){return"unitOfCodeWork"in t&&!function(e){return e.children.length>0&&"childIds"in e}(t)}(r)?[r]:r.children.flatMap(Rr)}function Ma(r,t,e=1e3){let s=null,i=0;const n=ut(t),o=()=>{const a=(()=>{const c=Date.now();if(s!==null&&c-i<e)return s;const h=r();return s=h,i=c,h})();n.set(a)};return{subscribe:n.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Dr=(r=>(r[r.unset=0]="unset",r[r.positive=1]="positive",r[r.negative=2]="negative",r))(Dr||{});function Ia(r){var e;if(!r)return Ps.IMAGE_FORMAT_UNSPECIFIED;switch((e=r.split("/")[1])==null?void 0:e.toLowerCase()){case"jpeg":case"jpg":return Ps.JPEG;case"png":return Ps.PNG;default:return Ps.IMAGE_FORMAT_UNSPECIFIED}}function Ea(r,t,e){var i,n;if(r.phase!==xt.cancelled&&r.phase!==xt.completed&&r.phase!==xt.error)return;let s;return(i=r.result)!=null&&i.contentNodes?(s=function(o,a){return o.map(c=>c.type===Nn.ContentText?{type:bi.CONTENT_TEXT,text_content:c.text_content}:c.type===Nn.ContentImage&&c.image_content&&a?{type:bi.CONTENT_IMAGE,image_content:{image_data:c.image_content.image_data,format:Ia(c.image_content.media_type)}}:{type:bi.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(r.result.contentNodes,e),{content:"",is_error:r.result.isError,request_id:r.result.requestId,tool_use_id:t,content_nodes:s}):((n=r.result)==null?void 0:n.text)!==void 0?{content:r.result.text,is_error:r.result.isError,request_id:r.result.requestId,tool_use_id:t}:void 0}function Ta(r=[]){const t=function(e=[]){let s;for(const i of e){if(i.type===pt.TOOL_USE)return i;i.type===pt.TOOL_USE_START&&(s=i)}return s}(r);return t&&t.type===pt.TOOL_USE?r.filter(e=>e.type!==pt.TOOL_USE_START):r}const ye="__NEW_AGENT__",Hn=r=>Mt(r)&&!!r.request_message;function He(r){var t;return((t=r.extraData)==null?void 0:t.isAgentConversation)===!0}var Ct=(r=>(r[r.active=0]="active",r[r.inactive=1]="inactive",r))(Ct||{});const Gt={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was summarized to reduce context size.
Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
<summary>
{summary}
</summary>
Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point."};class ka{constructor(t,e,s){l(this,"historySummaryVersion",2);l(this,"_callbacksManager",new Jo);l(this,"_params");this._conversationModel=t,this._extensionClient=e,this._chatFlagModel=s,this._params=Pn(s.historySummaryParams),s.subscribe(i=>{this._params=Pn(i.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}clearStaleHistorySummaryNodes(t){return t.filter(e=>!es(e)||e.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(t){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const e=this._params.cacheTTLMs-t-this._params.bufferTimeBeforeCacheExpirationMs;e>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},e)}preprocessChatHistory(t){const e=t.findLastIndex(s=>es(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(e>0&&(console.info(`Using history summary node found at index ${e} with requestId: ${t[e].request_id}`),t=t.slice(e)),t=t.filter(s=>!es(s)||s.summaryVersion===this.historySummaryVersion)):t=t.filter(s=>!es(s)),t}async maybeAddHistorySummaryNode(t=!1,e){var z,W,P;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",t),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),i=t?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",i),i<=0)return!1;const{head:n,tail:o,headSizeChars:a,tailSizeChars:c}=Yo(s,this._params.historyTailSizeCharsToExclude,i,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",a," tailSizeChars: ",c),n.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const h=vi(s),d=vi(n),u=vi(o),m={totalHistoryCharCount:h,totalHistoryExchangeCount:s.length,headCharCount:d,headExchangeCount:n.length,headLastRequestId:((z=n.at(-1))==null?void 0:z.request_id)??"",tailCharCount:u,tailExchangeCount:o.length,tailLastRequestId:((W=o.at(-1))==null?void 0:W.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:t,isAborted:!1};let p=((P=n.at(-1))==null?void 0:P.response_nodes)??[],C=p.filter(Y=>Y.type===pt.TOOL_USE);C.length>0&&(n.at(-1).response_nodes=p.filter(Y=>Y.type!==pt.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",n.length);const x=Date.now(),{responseText:S,requestId:y}=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:n}),_=Date.now();if(m.summaryCharCount=S.length,m.summarizationDurationMs=_-x,m.isAborted=!!(e!=null&&e.aborted),this._extensionClient.reportAgentRequestEvent({eventName:Fi.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:y??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:m}}),e==null?void 0:e.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!y||S.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;const w=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",S),N=this._params.summaryNodeResponseMessage,D={chatItemType:je.historySummary,summaryVersion:this.historySummaryVersion,request_id:y,request_message:w,response_text:N,structured_output_nodes:[{id:C.map(Y=>Y.id).reduce((Y,ot)=>Math.max(Y,ot),-1)+1,type:pt.RAW_RESPONSE,content:N},...C],status:ht.success,seen_state:ue.seen,timestamp:new Date().toISOString()},j=this._conversationModel.chatHistory.findLastIndex(Y=>Y.request_id===n.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",j),this._conversationModel.insertChatItem(j,D),!0}}function Pn(r){try{if(!r)return console.log("historySummaryParams is empty. Using default params"),Gt;const t=JSON.parse(r),e={triggerOnHistorySizeChars:t.trigger_on_history_size_chars||Gt.triggerOnHistorySizeChars,historyTailSizeCharsToExclude:t.history_tail_size_chars_to_exclude||Gt.historyTailSizeCharsToExclude,triggerOnHistorySizeCharsWhenCacheExpiring:t.trigger_on_history_size_chars_when_cache_expiring||Gt.triggerOnHistorySizeCharsWhenCacheExpiring,prompt:t.prompt||Gt.prompt,cacheTTLMs:t.cache_ttl_ms||Gt.cacheTTLMs,bufferTimeBeforeCacheExpirationMs:t.buffer_time_before_cache_expiration_ms||Gt.bufferTimeBeforeCacheExpirationMs,summaryNodeRequestMessageTemplate:t.summary_node_request_message_template||Gt.summaryNodeRequestMessageTemplate,summaryNodeResponseMessage:t.summary_node_response_message||Gt.summaryNodeResponseMessage};e.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),e.summaryNodeRequestMessageTemplate=Gt.summaryNodeRequestMessageTemplate);const s={...e,prompt:e.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",s),e}catch(t){return console.error("Failed to parse history_summary_params:",t),Gt}}const Si="temp-fe";class mt{constructor(t,e,s,i){l(this,"_state");l(this,"_subscribers",new Set);l(this,"_focusModel",new kr);l(this,"_onSendExchangeListeners",[]);l(this,"_onNewConversationListeners",[]);l(this,"_onHistoryDeleteListeners",[]);l(this,"_onBeforeChangeConversationListeners",[]);l(this,"_totalCharactersCacheThrottleMs",1e3);l(this,"_totalCharactersStore");l(this,"_chatHistorySummarizationModel");l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"setConversation",(t,e=!0,s=!0)=>{const i=t.id!==this._state.id;i&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,a])=>{if(a.requestId&&a.toolUseId){const{requestId:c,toolUseId:h}=Ln(o);return c===a.requestId&&h===a.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",$i(a)),[o,a]}return[o,{...a,...Ln(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&i&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const n=mt.isEmpty(t);if(i&&n){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(Mt)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),i&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});l(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});l(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});l(this,"setName",t=>{this.update({name:t})});l(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});l(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});l(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[$i(t)]:t}})});l(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:xt.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[$i({requestId:t,toolUseId:e})]||{phase:xt.new});l(this,"getLastToolUseId",()=>{var s,i;const t=this.lastExchange;if(!t)return;const e=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(n=>n.type===pt.TOOL_USE))??[]).at(-1);return e?(i=e.tool_use)==null?void 0:i.tool_use_id:void 0});l(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:xt.unknown};const e=function(i=[]){let n;for(const o of i){if(o.type===pt.TOOL_USE)return o;o.type===pt.TOOL_USE_START&&(n=o)}return n}(t==null?void 0:t.structured_output_nodes);return e?this.getToolUseState(t.request_id,(s=e.tool_use)==null?void 0:s.tool_use_id):{phase:xt.unknown}});l(this,"addExchange",(t,e)=>{const s=this._state.chatHistory;let i,n;i=e===void 0?[...s,t]:e===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,e),t,...s.slice(e)],Mt(t)&&(n=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Dr.unset,feedbackNote:""}}:void 0),this.update({chatHistory:i,...n?{feedbackStates:n}:{},lastUrl:void 0})});l(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});l(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});l(this,"updateExchangeById",(t,e,s=!1)=>{var a;const i=this.exchangeWithRequestId(e);if(i===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(i.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=Ta([...i.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==i.stop_reason&&i.stop_reason&&t.stop_reason===sa.REASON_UNSPECIFIED&&(t.stop_reason=i.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...i.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const n=(a=(t.structured_output_nodes||[]).find(c=>c.type===pt.MAIN_TEXT_FINISHED))==null?void 0:a.content;n&&n!==t.response_text&&(t.response_text=n);let o=this._state.isShareable||is({...i,...t});return this.update({chatHistory:this.chatHistory.map(c=>c.request_id===e?{...c,...t}:c),isShareable:o}),!0});l(this,"clearMessagesFromHistory",t=>{const e=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t),toolUseIds:e})});l(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});l(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),i=s.map(o=>o.request_id).filter(o=>o!==void 0),n=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:i,toolUseIds:n}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(a=>a(o))})});l(this,"clearMessageFromHistory",t=>{const e=this.chatHistory.find(i=>i.request_id===t),s=e?this._collectToolUseIdsFromMessages([e]):[];this.update({chatHistory:this.chatHistory.filter(i=>i.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t],toolUseIds:s})});l(this,"_collectToolUseIdsFromMessages",t=>{var s;const e=[];for(const i of t)if(Mt(i)&&i.structured_output_nodes)for(const n of i.structured_output_nodes)n.type===pt.TOOL_USE&&((s=n.tool_use)!=null&&s.tool_use_id)&&e.push(n.tool_use.tool_use_id);return e});l(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});l(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});l(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});l(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:ht.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));l(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});l(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);l(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});l(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:ue.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});l(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));l(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});l(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});l(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(i=>t.has(i.id)||i.recentFile||i.selection||i.sourceFolder),s=this._specialContextInputModel.recentItems.filter(i=>!(t.has(i.id)||i.recentFile||i.selection||i.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});l(this,"saveDraftExchange",(t,e)=>{var o,a,c;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),i=e!==((a=this.draftExchange)==null?void 0:a.rich_text_json_repr);if(!s&&!i)return;const n=(c=this.draftExchange)==null?void 0:c.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:n,status:ht.draft}})});l(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});l(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!He(this)){const i=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&i&&this.updateConversationTitle()}}).finally(()=>{var s;He(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Fi.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});l(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:ht.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});l(this,"sendInstructionExchange",async(t,e)=>{let s=`${Si}-${crypto.randomUUID()}`;const i={status:ht.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:ue.unseen,timestamp:new Date().toISOString()};this.addExchange(i);for await(const n of this._extensionClient.sendInstructionMessage(i,e)){if(!this.updateExchangeById(n,s,!0))return;s=n.request_id||s}});l(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});l(this,"checkAndGenerateAgentTitle",()=>{var e;if(!(!He(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>Hn(s))).length===1&&!((e=this.extraData)!=null&&e.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});l(this,"sendSummaryExchange",()=>{const t={status:ht.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:je.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});l(this,"generateCommitMessage",async()=>{let t=`${Si}-${crypto.randomUUID()}`;const e={status:ht.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:ue.unseen,chatItemType:je.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});l(this,"sendExchange",async(t,e=!1,s)=>{var d;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let i=`${Si}-${crypto.randomUUID()}`,n=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(mt.isNew(this._state)){const u=crypto.randomUUID(),m=this._state.id;try{await this._extensionClient.migrateConversationId(m,u)}catch(p){console.error("Failed to migrate conversation checkpoints:",p)}this._state={...this._state,id:u},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(u),this._subscribers.forEach(p=>p(this))}t=jn(t);let o={status:ht.sent,request_id:i,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:n,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:ue.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(u=>u(o)),this._chatFlagModel.useHistorySummary&&!t.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},i,!1);const a=Date.now();let c=!1;for await(const u of this.sendUserMessage(i,o,e,s)){if(((d=this.exchangeWithRequestId(i))==null?void 0:d.status)!==ht.sent||!this.updateExchangeById(u,i,!0))return;if(i=u.request_id||i,!c&&He(this)){const m=Date.now(),p=m-a;this._extensionClient.reportAgentRequestEvent({eventName:Fi.firstTokenReceived,conversationId:this.id,requestId:i,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:a,firstTokenReceivedTimestampMs:m,timeToFirstTokenMs:p}}}),c=!0}}const h=Date.now()-a;this._chatHistorySummarizationModel.maybeScheduleSummarization(h)});l(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:ht.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Oi.chatUseSuggestedQuestion)});l(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});l(this,"recoverExchange",async t=>{var i;if(!t.request_id||t.status!==ht.sent)return;let e=t.request_id;const s=(i=t.structured_output_nodes)==null?void 0:i.filter(n=>n.type===pt.AGENT_MEMORY);this.updateExchangeById({...t,response_text:t.lastChunkId?t.response_text:"",structured_output_nodes:t.lastChunkId?t.structured_output_nodes??[]:s},e);for await(const n of this.getChatStream(t)){if(!this.updateExchangeById(n,e,!0))return;e=n.request_id||e}});l(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{Mt(e)&&this._loadContextFromExchange(e)})});l(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});l(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{Mt(e)&&this._unloadContextFromExchange(e)})});l(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});l(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});l(this,"_jsonToStructuredRequest",t=>{const e=[],s=n=>{var a;const o=e.at(-1);if((o==null?void 0:o.type)===wt.TEXT){const c=((a=o.text_node)==null?void 0:a.content)??"",h={...o,text_node:{content:c+n}};e[e.length-1]=h}else e.push({id:e.length,type:wt.TEXT,text_node:{content:n}})},i=n=>{var o,a,c,h,d;if(n.type==="doc"||n.type==="paragraph")for(const u of n.content??[])i(u);else if(n.type==="hardBreak")s(`
`);else if(n.type==="text")s(n.text??"");else if(n.type==="file"){if(typeof((o=n.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(a=n.attrs)==null?void 0:a.src);if(n.attrs.isLoading)return;const u=(c=n.attrs)==null?void 0:c.title,m=na(u);Fr(u)?e.push({id:e.length,type:wt.IMAGE_ID,image_id_node:{image_id:n.attrs.src,format:m}}):e.push({id:e.length,type:wt.FILE_ID,file_id_node:{file_id:n.attrs.src,file_name:u}})}else if(n.type==="mention"){const u=(h=n.attrs)==null?void 0:h.data;u&&ii(u)?e.push({id:e.length,type:wt.TEXT,text_node:{content:zo(this._chatFlagModel,u.personality.type)}}):u&&wr(u)?e.push({id:e.length,type:wt.TEXT,text_node:{content:ta.getTaskOrchestratorPrompt(u.task)}}):s(`@\`${(u==null?void 0:u.name)??(u==null?void 0:u.id)}\``)}else if(n.type==="askMode"){const u=(d=n.attrs)==null?void 0:d.prompt;u&&e.push({id:e.length,type:wt.TEXT,text_node:{content:u}})}};return i(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=i,this._state={...mt.create()},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new ka(this,t,e)}get conversationId(){return this._state.id}insertChatItem(t,e){const s=[...this._state.chatHistory];s.splice(t,0,e),this.update({chatHistory:s})}_createTotalCharactersStore(){return Ma(()=>{let t=0;const e=this._state.chatHistory;return this.convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,i)=>s+i,0))||0)<=4?le.PROTOTYPER:le.DEFAULT}catch(e){return console.error("Error determining persona type:",e),le.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:le.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const e=t.chatHistory.find(Mt);return e&&e.request_message?mt.toSentenceCase(e.request_message):He(t)?"New Agent":"New Chat"}static isNew(t){return t.id===ye}static isEmpty(t){var i;const e=t.chatHistory.filter(n=>Mt(n)),s=t.chatHistory.filter(n=>Ai(n));return e.length===0&&s.length===0&&!((i=t.draftExchange)!=null&&i.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?mt.lastMessageTimestamp(t):e==="lastInteractedAt"?mt.lastInteractedAt(t):mt.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const e=(s=t.chatHistory.findLast(Mt))==null?void 0:s.timestamp;return e?new Date(e):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!mt.isEmpty(t)||mt.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const i of this._onBeforeChangeConversationListeners){const n=i(t,s);n!==void 0&&(s=n)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return mt.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??le.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return mt.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return mt.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=i=>Array.isArray(i)?i.some(e):!!i&&(i.type==="file"||!(!i.content||!Array.isArray(i.content))&&i.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(Mt)??null}get lastExchange(){return this.chatHistory.findLast(Mt)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>Mt(t)&&t.status===ht.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>is(t)||$s(t)||es(t))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(t){if(t.length===0)return[];t=this._chatHistorySummarizationModel.preprocessChatHistory(t);const e=[];for(const s of t)if(is(s))e.push(zn(s));else if(es(s))e.push(zn(s));else if($s(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const i=Aa(s,1),n={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};e.push(n)}return e}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===ht.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,i="";const n=await this._addIdeStateNode(jn({...t,request_id:e,status:ht.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(e,n,!0))o.response_text&&(i+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:i,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t.request_id,t.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}_resolveUnresolvedToolUses(t,e,s){var d,u,m;if(t.length===0)return[t,e];const i=t[t.length-1],n=((d=i.response_nodes)==null?void 0:d.filter(p=>p.type===pt.TOOL_USE))??[];if(n.length===0)return[t,e];const o=new Set;(u=e.structured_request_nodes)==null||u.forEach(p=>{var C;p.type===wt.TOOL_RESULT&&((C=p.tool_result_node)!=null&&C.tool_use_id)&&o.add(p.tool_result_node.tool_use_id)});const a=n.filter(p=>{var x;const C=(x=p.tool_use)==null?void 0:x.tool_use_id;return C&&!o.has(C)});if(a.length===0)return[t,e];const c=a.map((p,C)=>{const x=p.tool_use.tool_use_id;return function(S,y,_,w){const N=Ea(y,S,w);let D;if(N!==void 0)D=N;else{let j;switch(y.phase){case xt.runnable:j="Tool was cancelled before running.";break;case xt.new:j="Cancelled by user.";break;case xt.checkingSafety:j="Tool was cancelled during safety check.";break;case xt.running:j="Tool was cancelled while running.";break;case xt.cancelling:j="Tool cancellation was interrupted.";break;case xt.cancelled:j="Cancelled by user.";break;case xt.error:j="Tool execution failed.";break;case xt.completed:j="Tool completed but result was unavailable.";break;case xt.unknown:default:j="Cancelled by user.",y.phase!==xt.unknown&&console.error(`Unexpected tool state phase: ${y.phase}`)}D={tool_use_id:S,content:j,is_error:!0}}return{id:_,type:wt.TOOL_RESULT,tool_result_node:D}}(x,this.getToolUseState(i.request_id,x),Ri(e.structured_request_nodes??[])+C+1,this._chatFlagModel.enableDebugFeatures)});if((m=e.structured_request_nodes)==null?void 0:m.some(p=>p.type===wt.TOOL_RESULT))return[t,{...e,structured_request_nodes:[...e.structured_request_nodes??[],...c]}];{const p={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:c,structured_output_nodes:[],status:ht.success,hidden:!0};return s||this.addExchangeBeforeLast(p),[t.concat(this.convertHistoryToExchanges([p])),e]}}async*sendUserMessage(t,e,s,i){var u;const n=this._specialContextInputModel.chatActiveContext;let o;if(e.chatHistory!==void 0)o=e.chatHistory;else{let m=this.successfulMessages;if(e.chatItemType===je.summaryTitle){const p=m.findIndex(C=>C.chatItemType!==je.agentOnboarding&&Hn(C));p!==-1&&(m=m.slice(p))}o=this.convertHistoryToExchanges(m)}this._chatFlagModel.enableParallelTools&&([o,e]=this._resolveUnresolvedToolUses(o,e,s));let a=this.personaType;if(e.structured_request_nodes){const m=e.structured_request_nodes.find(p=>p.type===wt.CHANGE_PERSONALITY);m&&m.change_personality_node&&(a=m.change_personality_node.personality_type)}const c={text:e.request_message,chatHistory:o,silent:s,modelId:e.model_id,context:n,userSpecifiedFiles:n.userSpecifiedFiles,externalSourceIds:(u=n.externalSources)==null?void 0:u.map(m=>m.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:a,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:i},h=this._createStreamStateHandlers(t,c,{flags:this._chatFlagModel}),d=this._extensionClient.startChatStreamWithRetry(t,c,{flags:this._chatFlagModel});for await(const m of d){let p=m;t=m.request_id||t;for(const C of h)p=C.handleChunk(p)??p;yield p}for(const m of h)yield*m.handleComplete();this.updateExchangeById({structured_request_nodes:e.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}async _addIdeStateNode(t){let e,s=(t.structured_request_nodes??[]).filter(i=>i.type!==wt.IDE_STATE);try{e=await this._extensionClient.getChatRequestIdeState()}catch(i){console.error("Failed to add IDE state to exchange:",i)}return e?(s=[...s,{id:Ri(s)+1,type:wt.IDE_STATE,ide_state_node:e}],{...t,structured_request_nodes:s}):t}}function Aa(r,t){const e=($s(r),r.fromTimestamp),s=($s(r),r.toTimestamp),i=$s(r)&&r.revertTarget!==void 0;return{id:t,type:wt.CHECKPOINT_REF,checkpoint_ref_node:{request_id:r.request_id||"",from_timestamp:e,to_timestamp:s,source:i?ia.CHECKPOINT_REVERT:void 0}}}function zn(r){const t=(r.structured_output_nodes??[]).filter(e=>e.type===pt.RAW_RESPONSE||e.type===pt.TOOL_USE||e.type===pt.TOOL_USE_START).map(e=>e.type===pt.TOOL_USE_START?{...e,tool_use:{...e.tool_use,input_json:"{}"},type:pt.TOOL_USE}:e);return{request_message:r.request_message,response_text:r.response_text??"",request_id:r.request_id||"",request_nodes:r.structured_request_nodes??[],response_nodes:t}}function Ri(r){return r.length>0?Math.max(...r.map(t=>t.id)):0}function jn(r){var t;if(r.request_message.length>0&&!((t=r.structured_request_nodes)!=null&&t.some(e=>e.type===wt.TEXT))){let e=r.structured_request_nodes??[];return e=[...e,{id:Ri(e)+1,type:wt.TEXT,text_node:{content:r.request_message}}],{...r,structured_request_nodes:e}}return r}class Fa{constructor(t=!0,e=setTimeout){l(this,"_notify",new Set);l(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});l(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});l(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});l(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,i){if(!t&&!i)return()=>{};const n={timeout:t,notify:e,once:s,date:i};return this._notify.add(n),this._schedule(n),()=>{this._clearTimeout(n),this._notify.delete(n)}}}class Oa{constructor(t=0,e=0,s=new Fa,i=ut("busy"),n=ut(!1)){l(this,"unsubNotify");l(this,"unsubMessage");l(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});l(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=i,this.focusAfterIdle=n,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var Ws=(r=>(r.send="send",r.addTask="addTask",r))(Ws||{});class Na{constructor(){l(this,"_mode",ut(Ws.send));l(this,"_currentMode",Ws.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(Ws).includes(t)&&this._mode.set(t)}}class La{constructor(t=3e5){l(this,"_cleanItems",new Set);l(this,"_lastProcessedTime",new Map);this.cooldownMs=t}markClean(t){this._cleanItems.add(t),this._lastProcessedTime.set(t,Date.now())}markDirty(t){this._cleanItems.delete(t),this._lastProcessedTime.delete(t)}isClean(t){return this._cleanItems.has(t)}isWithinCooldown(t){const e=this._lastProcessedTime.get(t);return!!e&&Date.now()-e<this.cooldownMs}getLastProcessedTime(t){return this._lastProcessedTime.get(t)||0}cleanup(t){const e=Array.isArray(t)?t:Array.from(t);for(const s of e)this.markDirty(s)}clear(){this._cleanItems.clear(),this._lastProcessedTime.clear()}getStats(){return{cleanCount:this._cleanItems.size,trackedCount:this._lastProcessedTime.size}}}class Ra{constructor(t,e,s=new La(3e5)){l(this,"_maxItemsPerIteration",5);this._extensionClient=t,this._flags=e,this._cache=s}async hydrateConversation(t){let e=t;return e=await this._hydrateExchanges(e),e=await this._hydrateToolUseStates(e),this._cache.markDirty(t.id),e}async dehydrateConversation(t){let e=t;return this._flags.enableExchangeStorage&&(e=await this._dehydrateExchanges(e)),this._flags.enableToolUseStateStorage&&(e=await this._dehydrateToolUseStates(e)),e}async dehydrateConversationsIncremental(t,e){if(!this._flags.enableExchangeStorage&&!this._flags.enableToolUseStateStorage)return t;const s=Object.entries(t),i=this._selectConversationsForDehydration(s,e),n={};for(const[o,a]of s)if(i.includes(o))try{const c=await this.dehydrateConversation(a);this._cache.markClean(o),n[o]=o===e?a:c}catch(c){console.warn(`Failed to dehydrate conversation ${o}:`,c),n[o]=a}else n[o]=a;return n}async hydrateCurrentConversation(t,e){if(!e||!t[e])return t;try{const s=await this.hydrateConversation(t[e]);return{...t,[e]:s}}catch(s){return console.warn(`Failed to hydrate conversation ${e}:`,s),t}}markNeedsDehydration(t){this._cache.markDirty(t)}markDehydrated(t){this._cache.markClean(t)}cleanupDeletedConversations(t){this._cache.cleanup(t)}_selectConversationsForDehydration(t,e){var n;const s=[];if(e){const o=(n=t.find(([a])=>a===e))==null?void 0:n[1];o&&!this._cache.isClean(e)&&(o.chatHistory.some(Mt)?s.push(e):this._cache.markClean(e))}const i=t.filter(([o,a])=>o===e||this._cache.isClean(o)?!1:a.chatHistory.some(Mt)?!this._cache.isWithinCooldown(o):(this._cache.markClean(o),!1)).map(([o])=>o).sort((o,a)=>this._cache.getLastProcessedTime(o)-this._cache.getLastProcessedTime(a));return[...s,...i].slice(0,this._maxItemsPerIteration)}async _dehydrateExchanges(t){var i;const e=[],s=[];for(const n of t.chatHistory)if(Mt(n)){const o=n,a=o.request_id||((i=crypto==null?void 0:crypto.randomUUID)==null?void 0:i.call(crypto))||ra(),c={request_message:o.request_message,response_text:o.response_text||"",request_id:a,request_nodes:o.structured_request_nodes,response_nodes:o.structured_output_nodes,uuid:a,conversationId:t.id,status:o.status===ht.success?"success":o.status===ht.failed?"failed":"sent",timestamp:o.timestamp||new Date().toISOString(),seen_state:o.seen_state===ue.seen?"seen":"unseen"};e.push(c);const h={chatItemType:je.exchangePointer,exchangeUuid:a,timestamp:o.timestamp,request_message:o.request_message,status:o.status,hasResponse:!!o.response_text,isStreaming:o.status===ht.sent,seen_state:o.seen_state};s.push(h)}else s.push(n);if(e.length>0)try{await this._extensionClient.saveExchanges(t.id,e)}catch{return t}return{...t,chatHistory:s}}async _dehydrateToolUseStates(t){if(!this._flags.enableToolUseStateStorage||!t.toolUseStates||Object.keys(t.toolUseStates).length===0)return t;try{return await this._extensionClient.saveToolUseStates(t.id,t.toolUseStates),{...t,toolUseStates:{}}}catch(e){return console.warn(`Failed to store tool use states for conversation ${t.id}:`,e),t}}async _hydrateExchanges(t){const e=t.chatHistory.filter(Ai);if(e.length===0)return t;try{const s=e.map(a=>a.exchangeUuid),i=await this._extensionClient.loadExchanges(t.id,s),n=new Map(i.map(a=>[a.uuid,a])),o=t.chatHistory.map(a=>{if(Ai(a)){const c=n.get(a.exchangeUuid);if(c)return{request_message:c.request_message,response_text:c.response_text,request_id:c.request_id,structured_request_nodes:c.request_nodes,structured_output_nodes:c.response_nodes,timestamp:c.timestamp,status:c.status==="success"?ht.success:c.status==="failed"?ht.failed:ht.sent,seen_state:c.seen_state==="seen"?ue.seen:ue.unseen}}return a});return{...t,chatHistory:o}}catch(s){return console.warn(`Failed to restore exchanges for conversation ${t.id}:`,s),t}}async _hydrateToolUseStates(t){try{const e=await this._extensionClient.loadConversationToolUseStates(t.id);return{...t,toolUseStates:{...t.toolUseStates,...e}}}catch(e){return console.warn(`Failed to restore tool use states for conversation ${t.id}:`,e),t}}}const zs=ut("idle");class en{constructor(t,e,s,i={}){l(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});l(this,"extensionClient");l(this,"_chatFlagsModel");l(this,"_currConversationModel");l(this,"_chatModeModel");l(this,"_sendModeModel");l(this,"_flagsLoaded",ut(!1));l(this,"_persistenceController");l(this,"subscribers",new Set);l(this,"idleMessageModel",new Oa);l(this,"isPanelCollapsed");l(this,"agentExecutionMode");l(this,"sortConversationsBy");l(this,"displayedAnnouncements");l(this,"onLoaded",async()=>{var e,s;const t=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,enableAgentSwarmMode:t.enableAgentSwarmMode??!1,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??Go,bigSyncThreshold:t.bigSyncThreshold??Vo,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Bo,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??qo.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,enableExchangeStorage:t.enableExchangeStorage??!1,enableToolUseStateStorage:t.enableToolUseStateStorage??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryParams:t.historySummaryParams??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0,retryChatStreamTimeouts:t.retryChatStreamTimeouts??!1,enableCommitIndexing:t.enableCommitIndexing??!1,enableMemoryRetrieval:t.enableMemoryRetrieval??!1,isVscodeVersionOutdated:t.isVscodeVersionOutdated??!1,vscodeMinVersion:t.vscodeMinVersion??"",enableAgentTabs:t.enableAgentTabs??!1,enableAgentGitTracker:t.enableAgentGitTracker??!1,remoteAgentsResumeHintAvailableTtlDays:t.remoteAgentsResumeHintAvailableTtlDays??0,enableParallelTools:t.enableParallelTools??!1,memoriesParams:t.memoriesParams??{}}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._flagsLoaded.set(!0),await this.initializeAsync(this.options.initialConversation),(s=(e=this.options).onLoaded)==null||s.call(e),this.notifySubscribers()});l(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));l(this,"initializeSync",t=>{if(this._state={...this._state,...this._host.getState()},t&&(this._state.conversations={...this._state.conversations,[t.id]:t}),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==_i&&this.currentConversationId!==_i||(delete this._state.conversations[_i],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===ye||mt.isValid(s))),this.currentConversationId&&this.currentConversationId!==this.currentConversationModel.id){const e=this.conversations[this.currentConversationId];e&&this.currentConversationModel.setConversation(e)}this.initializeIsShareableState(),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});l(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const i=s.chatHistory.some(n=>is(n));t[e]={...s,isShareable:i}}this._state.conversations=t});l(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[i,n]of Object.entries(e))n.isPinned&&s.add(i);this.setState(this._state),this.notifySubscribers()});l(this,"saveImmediate",()=>{this.setState(this._state),this.setState.flush()});l(this,"setState");l(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});l(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));l(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:je.educateFeatures,request_id:crypto.randomUUID(),seen_state:ue.seen})});l(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});l(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let i;t===void 0&&(t=ye);const n=this._state.conversations[t];i=n?await this._persistenceController.hydrateConversation(n):mt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===ye&&(i.id=ye),s!=null&&s.newTaskUuid&&(i.rootTaskUuid=s.newTaskUuid);const o=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(i,!o,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});l(this,"saveConversation",async(t,e)=>{this._persistenceController.markNeedsDehydration(t.id),this.updateChatState({...this._state,currentConversationId:t.id,conversations:{...this._state.conversations,[t.id]:t}}),e&&delete this._state.conversations[ye]});l(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});l(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});l(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;zs.set("copying");const s=e==null?void 0:e.chatHistory,i=s.reduce((a,c)=>(is(c)&&a.push({request_id:c.request_id||"",request_message:c.request_message,response_text:c.response_text||""}),a),[]);if(i.length===0)throw new Error("No chat history to share");const n=mt.getDisplayName(e),o=await this.extensionClient.saveChat(t,i,n);if(o.data){let a=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:a}}}),a}throw new Error("Failed to create URL")});l(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void zs.set("idle");navigator.clipboard.writeText(e),zs.set("copied")}catch{zs.set("failed")}});l(this,"deleteConversations",async(t,e=void 0,s=[],i)=>{const n=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${n>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const o=new Set(t);await this.deleteConversationIds(o)}if(s.length>0&&i)for(const o of s)try{await i.deleteAgent(o,!0)}catch(a){console.error(`Failed to delete remote agent ${o}:`,a)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});l(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});l(this,"deleteConversationIds",async t=>{var i,n,o;const e=[],s=[];for(const a of t){const c=((i=this._state.conversations[a])==null?void 0:i.requestIds)??[];e.push(...c);const h=((n=this._state.conversations[a])==null?void 0:n.toolUseStates)??{};for(const u of Object.keys(h)){const{toolUseId:m}=h[u];m&&s.push(m)}const d=this._state.conversations[a];if(d){for(const u of d.chatHistory)if(Mt(u)&&u.structured_output_nodes)for(const m of u.structured_output_nodes)m.type===pt.TOOL_USE&&((o=m.tool_use)!=null&&o.tool_use_id)&&s.push(m.tool_use.tool_use_id)}}for(const a of Object.values(this._state.conversations))if(t.has(a.id)){for(const h of a.chatHistory)Mt(h)&&this.deleteImagesInExchange(h);const c=a.draftExchange;c&&this.deleteImagesInExchange(c)}for(const a of t){try{await this.extensionClient.deleteConversationExchanges(a)}catch(c){console.error(`Failed to delete exchanges for conversation ${a}:`,c)}if(this.flags.enableToolUseStateStorage)try{await this.extensionClient.deleteConversationToolUseStates(a)}catch(c){console.error(`Failed to delete tool use states for conversation ${a}:`,c)}}this._persistenceController.cleanupDeletedConversations(t),this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([a])=>!t.has(a)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t),toolUseIds:s})});l(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});l(this,"findImagesInJson",t=>{const e=[],s=i=>{var n,o;if(i.type==="file"&&((n=i.attrs)!=null&&n.src)){const a=(o=i.attrs)==null?void 0:o.src;Fr(a)&&e.push(i.attrs.src)}else if((i.type==="doc"||i.type==="paragraph")&&i.content)for(const a of i.content)s(a)};return s(t),e});l(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===wt.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));l(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});l(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});l(this,"smartPaste",(t,e,s,i)=>{const n=this._currConversationModel.historyTo(t,!0).filter(o=>is(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:n,targetFile:s??void 0,options:i})});l(this,"saveImage",async t=>await this.extensionClient.saveImage(t));l(this,"saveAttachment",async t=>await this.extensionClient.saveAttachment(t));l(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));l(this,"renderImage",async t=>await this.extensionClient.loadImage(t));var c,h;this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=i,this._chatFlagsModel=new jo(i.initialFlags),this.extensionClient=new Wo(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new mt(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new Na,this._persistenceController=new Ra(this.extensionClient,this._chatFlagsModel);const n=((c=i.debounceConfig)==null?void 0:c.wait)??5e3,o=((h=i.debounceConfig)==null?void 0:h.maxWait)??3e4;this.setState=ki(d=>{this._setStateWithPersistence(d)},n,{maxWait:o}),this.initializeSync(i.initialConversation);const a=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=ut(a),this.agentExecutionMode=ut(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=ut(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=ut(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get flagsLoaded(){return this._flagsLoaded}async initializeAsync(t){const e=(t==null?void 0:t.id)||this.currentConversationId;this._state.conversations=await this._persistenceController.hydrateCurrentConversation(this._state.conversations,e),t?await this.setCurrentConversation(t.id):await this.setCurrentConversation(this.currentConversationId)}async _setStateWithPersistence(t){const e=await this._persistenceController.dehydrateConversationsIncremental(t.conversations??this._state.conversations,this.currentConversationId);this._host.setState({...t,conversations:e})}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const i=t||this._state.sortConversationsBy||"lastMessageTimestamp";let n=Object.values(this._state.conversations);return s&&(n=n.filter(s)),n.sort((o,a)=>{const c=mt.getTime(o,i).getTime(),h=mt.getTime(a,i).getTime();return e==="asc"?c-h:h-c})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===ye)return!1;const i=!mt.isValid(this.conversations[s]),n=He(this.conversations[s]);return i&&(t==="agent"&&n||t==="chat"&&!n||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===lt.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.handleSetToThreadType("localAgent","manual"):s.toLowerCase()==="chat"?await this._chatModeModel.handleSetToThreadType("chat"):console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}l(en,"NEW_AGENT_KEY",ye);function Wn(r,t){let e,s,i=t;const n=()=>i.editor.getModifiedEditor(),o=()=>{const{afterLineNumber:a}=i,c=n();if(a===void 0)return void c.changeViewZones(d=>{e&&c&&s&&d.removeZone(s)});const h={...i,afterLineNumber:a,domNode:r,suppressMouseDown:!0};c==null||c.changeViewZones(d=>{e&&s&&d.removeZone(s),s=d.addZone(h),e=h})};return o(),{update:a=>{i=a,o()},destroy:()=>{const a=n();a.changeViewZones(c=>{if(e&&a&&s)try{c.removeZone(s)}catch(h){if(h instanceof Error){if(h.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${h}`)}})}}}var Te=(r=>(r.edit="edit",r.instruction="instruction",r))(Te||{}),Di=(r=>(r[r.instructionDrawer=0]="instructionDrawer",r[r.chunkActionPanel=1]="chunkActionPanel",r))(Di||{});const Je=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Gn=new Set,qi=typeof process=="object"&&process?process:{},qr=(r,t,e,s)=>{typeof qi.emitWarning=="function"?qi.emitWarning(r,t,e,s):console.error(`[${e}] ${t}: ${r}`)};let ti=globalThis.AbortController,Vn=globalThis.AbortSignal;var _r;if(ti===void 0){Vn=class{constructor(){l(this,"onabort");l(this,"_onabort",[]);l(this,"reason");l(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},ti=class{constructor(){l(this,"signal",new Vn);t()}abort(e){var s,i;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const n of this.signal._onabort)n(e);(i=(s=this.signal).onabort)==null||i.call(s,e)}}};let r=((_r=qi.env)==null?void 0:_r.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{r&&(r=!1,qr("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const ve=r=>r&&r===Math.floor(r)&&r>0&&isFinite(r),Ur=r=>ve(r)?r<=Math.pow(2,8)?Uint8Array:r<=Math.pow(2,16)?Uint16Array:r<=Math.pow(2,32)?Uint32Array:r<=Number.MAX_SAFE_INTEGER?Gs:null:null;class Gs extends Array{constructor(t){super(t),this.fill(0)}}var ns;const ze=class ze{constructor(t,e){l(this,"heap");l(this,"length");if(!f(ze,ns))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=Ur(t);if(!e)return[];R(ze,ns,!0);const s=new ze(t,e);return R(ze,ns,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};ns=new WeakMap,B(ze,ns,!1);let Ui=ze;var yr,vr,Vt,Lt,Bt,Kt,rs,os,$t,Qt,ft,rt,H,At,Rt,Et,bt,Xt,St,Zt,Jt,Dt,Yt,we,Ft,I,Pi,We,ce,bs,qt,Hr,Ge,as,Ss,$e,Ce,zi,Vs,Bs,nt,ji,ys,be,Wi;const rn=class rn{constructor(t){B(this,I);B(this,Vt);B(this,Lt);B(this,Bt);B(this,Kt);B(this,rs);B(this,os);l(this,"ttl");l(this,"ttlResolution");l(this,"ttlAutopurge");l(this,"updateAgeOnGet");l(this,"updateAgeOnHas");l(this,"allowStale");l(this,"noDisposeOnSet");l(this,"noUpdateTTL");l(this,"maxEntrySize");l(this,"sizeCalculation");l(this,"noDeleteOnFetchRejection");l(this,"noDeleteOnStaleGet");l(this,"allowStaleOnFetchAbort");l(this,"allowStaleOnFetchRejection");l(this,"ignoreFetchAbort");B(this,$t);B(this,Qt);B(this,ft);B(this,rt);B(this,H);B(this,At);B(this,Rt);B(this,Et);B(this,bt);B(this,Xt);B(this,St);B(this,Zt);B(this,Jt);B(this,Dt);B(this,Yt);B(this,we);B(this,Ft);B(this,We,()=>{});B(this,ce,()=>{});B(this,bs,()=>{});B(this,qt,()=>!1);B(this,Ge,t=>{});B(this,as,(t,e,s)=>{});B(this,Ss,(t,e,s,i)=>{if(s||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});l(this,yr,"LRUCache");const{max:e=0,ttl:s,ttlResolution:i=1,ttlAutopurge:n,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:c,dispose:h,disposeAfter:d,noDisposeOnSet:u,noUpdateTTL:m,maxSize:p=0,maxEntrySize:C=0,sizeCalculation:x,fetchMethod:S,memoMethod:y,noDeleteOnFetchRejection:_,noDeleteOnStaleGet:w,allowStaleOnFetchRejection:N,allowStaleOnFetchAbort:D,ignoreFetchAbort:j}=t;if(e!==0&&!ve(e))throw new TypeError("max option must be a nonnegative integer");const z=e?Ur(e):Array;if(!z)throw new Error("invalid max value: "+e);if(R(this,Vt,e),R(this,Lt,p),this.maxEntrySize=C||f(this,Lt),this.sizeCalculation=x,this.sizeCalculation){if(!f(this,Lt)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(y!==void 0&&typeof y!="function")throw new TypeError("memoMethod must be a function if defined");if(R(this,os,y),S!==void 0&&typeof S!="function")throw new TypeError("fetchMethod must be a function if specified");if(R(this,rs,S),R(this,we,!!S),R(this,ft,new Map),R(this,rt,new Array(e).fill(void 0)),R(this,H,new Array(e).fill(void 0)),R(this,At,new z(e)),R(this,Rt,new z(e)),R(this,Et,0),R(this,bt,0),R(this,Xt,Ui.create(e)),R(this,$t,0),R(this,Qt,0),typeof h=="function"&&R(this,Bt,h),typeof d=="function"?(R(this,Kt,d),R(this,St,[])):(R(this,Kt,void 0),R(this,St,void 0)),R(this,Yt,!!f(this,Bt)),R(this,Ft,!!f(this,Kt)),this.noDisposeOnSet=!!u,this.noUpdateTTL=!!m,this.noDeleteOnFetchRejection=!!_,this.allowStaleOnFetchRejection=!!N,this.allowStaleOnFetchAbort=!!D,this.ignoreFetchAbort=!!j,this.maxEntrySize!==0){if(f(this,Lt)!==0&&!ve(f(this,Lt)))throw new TypeError("maxSize must be a positive integer if specified");if(!ve(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");A(this,I,Hr).call(this)}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!w,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=ve(i)||i===0?i:1,this.ttlAutopurge=!!n,this.ttl=s||0,this.ttl){if(!ve(this.ttl))throw new TypeError("ttl must be a positive integer if specified");A(this,I,Pi).call(this)}if(f(this,Vt)===0&&this.ttl===0&&f(this,Lt)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!f(this,Vt)&&!f(this,Lt)){const W="LRU_CACHE_UNBOUNDED";(P=>!Gn.has(P))(W)&&(Gn.add(W),qr("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",W,rn))}}static unsafeExposeInternals(t){return{starts:f(t,Jt),ttls:f(t,Dt),sizes:f(t,Zt),keyMap:f(t,ft),keyList:f(t,rt),valList:f(t,H),next:f(t,At),prev:f(t,Rt),get head(){return f(t,Et)},get tail(){return f(t,bt)},free:f(t,Xt),isBackgroundFetch:e=>{var s;return A(s=t,I,nt).call(s,e)},backgroundFetch:(e,s,i,n)=>{var o;return A(o=t,I,Bs).call(o,e,s,i,n)},moveToTail:e=>{var s;return A(s=t,I,ys).call(s,e)},indexes:e=>{var s;return A(s=t,I,$e).call(s,e)},rindexes:e=>{var s;return A(s=t,I,Ce).call(s,e)},isStale:e=>{var s;return f(s=t,qt).call(s,e)}}}get max(){return f(this,Vt)}get maxSize(){return f(this,Lt)}get calculatedSize(){return f(this,Qt)}get size(){return f(this,$t)}get fetchMethod(){return f(this,rs)}get memoMethod(){return f(this,os)}get dispose(){return f(this,Bt)}get disposeAfter(){return f(this,Kt)}getRemainingTTL(t){return f(this,ft).has(t)?1/0:0}*entries(){for(const t of A(this,I,$e).call(this))f(this,H)[t]===void 0||f(this,rt)[t]===void 0||A(this,I,nt).call(this,f(this,H)[t])||(yield[f(this,rt)[t],f(this,H)[t]])}*rentries(){for(const t of A(this,I,Ce).call(this))f(this,H)[t]===void 0||f(this,rt)[t]===void 0||A(this,I,nt).call(this,f(this,H)[t])||(yield[f(this,rt)[t],f(this,H)[t]])}*keys(){for(const t of A(this,I,$e).call(this)){const e=f(this,rt)[t];e===void 0||A(this,I,nt).call(this,f(this,H)[t])||(yield e)}}*rkeys(){for(const t of A(this,I,Ce).call(this)){const e=f(this,rt)[t];e===void 0||A(this,I,nt).call(this,f(this,H)[t])||(yield e)}}*values(){for(const t of A(this,I,$e).call(this))f(this,H)[t]===void 0||A(this,I,nt).call(this,f(this,H)[t])||(yield f(this,H)[t])}*rvalues(){for(const t of A(this,I,Ce).call(this))f(this,H)[t]===void 0||A(this,I,nt).call(this,f(this,H)[t])||(yield f(this,H)[t])}[(vr=Symbol.iterator,yr=Symbol.toStringTag,vr)](){return this.entries()}find(t,e={}){for(const s of A(this,I,$e).call(this)){const i=f(this,H)[s],n=A(this,I,nt).call(this,i)?i.__staleWhileFetching:i;if(n!==void 0&&t(n,f(this,rt)[s],this))return this.get(f(this,rt)[s],e)}}forEach(t,e=this){for(const s of A(this,I,$e).call(this)){const i=f(this,H)[s],n=A(this,I,nt).call(this,i)?i.__staleWhileFetching:i;n!==void 0&&t.call(e,n,f(this,rt)[s],this)}}rforEach(t,e=this){for(const s of A(this,I,Ce).call(this)){const i=f(this,H)[s],n=A(this,I,nt).call(this,i)?i.__staleWhileFetching:i;n!==void 0&&t.call(e,n,f(this,rt)[s],this)}}purgeStale(){let t=!1;for(const e of A(this,I,Ce).call(this,{allowStale:!0}))f(this,qt).call(this,e)&&(A(this,I,be).call(this,f(this,rt)[e],"expire"),t=!0);return t}info(t){const e=f(this,ft).get(t);if(e===void 0)return;const s=f(this,H)[e],i=A(this,I,nt).call(this,s)?s.__staleWhileFetching:s;if(i===void 0)return;const n={value:i};if(f(this,Dt)&&f(this,Jt)){const o=f(this,Dt)[e],a=f(this,Jt)[e];if(o&&a){const c=o-(Je.now()-a);n.ttl=c,n.start=Date.now()}}return f(this,Zt)&&(n.size=f(this,Zt)[e]),n}dump(){const t=[];for(const e of A(this,I,$e).call(this,{allowStale:!0})){const s=f(this,rt)[e],i=f(this,H)[e],n=A(this,I,nt).call(this,i)?i.__staleWhileFetching:i;if(n===void 0||s===void 0)continue;const o={value:n};if(f(this,Dt)&&f(this,Jt)){o.ttl=f(this,Dt)[e];const a=Je.now()-f(this,Jt)[e];o.start=Math.floor(Date.now()-a)}f(this,Zt)&&(o.size=f(this,Zt)[e]),t.unshift([s,o])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const i=Date.now()-s.start;s.start=Je.now()-i}this.set(e,s.value,s)}}set(t,e,s={}){var m,p,C,x,S;if(e===void 0)return this.delete(t),this;const{ttl:i=this.ttl,start:n,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:c}=s;let{noUpdateTTL:h=this.noUpdateTTL}=s;const d=f(this,Ss).call(this,t,e,s.size||0,a);if(this.maxEntrySize&&d>this.maxEntrySize)return c&&(c.set="miss",c.maxEntrySizeExceeded=!0),A(this,I,be).call(this,t,"set"),this;let u=f(this,$t)===0?void 0:f(this,ft).get(t);if(u===void 0)u=f(this,$t)===0?f(this,bt):f(this,Xt).length!==0?f(this,Xt).pop():f(this,$t)===f(this,Vt)?A(this,I,Vs).call(this,!1):f(this,$t),f(this,rt)[u]=t,f(this,H)[u]=e,f(this,ft).set(t,u),f(this,At)[f(this,bt)]=u,f(this,Rt)[u]=f(this,bt),R(this,bt,u),Hs(this,$t)._++,f(this,as).call(this,u,d,c),c&&(c.set="add"),h=!1;else{A(this,I,ys).call(this,u);const y=f(this,H)[u];if(e!==y){if(f(this,we)&&A(this,I,nt).call(this,y)){y.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:_}=y;_===void 0||o||(f(this,Yt)&&((m=f(this,Bt))==null||m.call(this,_,t,"set")),f(this,Ft)&&((p=f(this,St))==null||p.push([_,t,"set"])))}else o||(f(this,Yt)&&((C=f(this,Bt))==null||C.call(this,y,t,"set")),f(this,Ft)&&((x=f(this,St))==null||x.push([y,t,"set"])));if(f(this,Ge).call(this,u),f(this,as).call(this,u,d,c),f(this,H)[u]=e,c){c.set="replace";const _=y&&A(this,I,nt).call(this,y)?y.__staleWhileFetching:y;_!==void 0&&(c.oldValue=_)}}else c&&(c.set="update")}if(i===0||f(this,Dt)||A(this,I,Pi).call(this),f(this,Dt)&&(h||f(this,bs).call(this,u,i,n),c&&f(this,ce).call(this,c,u)),!o&&f(this,Ft)&&f(this,St)){const y=f(this,St);let _;for(;_=y==null?void 0:y.shift();)(S=f(this,Kt))==null||S.call(this,..._)}return this}pop(){var t;try{for(;f(this,$t);){const e=f(this,H)[f(this,Et)];if(A(this,I,Vs).call(this,!0),A(this,I,nt).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(f(this,Ft)&&f(this,St)){const e=f(this,St);let s;for(;s=e==null?void 0:e.shift();)(t=f(this,Kt))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:i}=e,n=f(this,ft).get(t);if(n!==void 0){const o=f(this,H)[n];if(A(this,I,nt).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!f(this,qt).call(this,n))return s&&f(this,We).call(this,n),i&&(i.has="hit",f(this,ce).call(this,i,n)),!0;i&&(i.has="stale",f(this,ce).call(this,i,n))}else i&&(i.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,i=f(this,ft).get(t);if(i===void 0||!s&&f(this,qt).call(this,i))return;const n=f(this,H)[i];return A(this,I,nt).call(this,n)?n.__staleWhileFetching:n}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:c=0,sizeCalculation:h=this.sizeCalculation,noUpdateTTL:d=this.noUpdateTTL,noDeleteOnFetchRejection:u=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:m=this.allowStaleOnFetchRejection,ignoreFetchAbort:p=this.ignoreFetchAbort,allowStaleOnFetchAbort:C=this.allowStaleOnFetchAbort,context:x,forceRefresh:S=!1,status:y,signal:_}=e;if(!f(this,we))return y&&(y.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:n,status:y});const w={allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:n,ttl:o,noDisposeOnSet:a,size:c,sizeCalculation:h,noUpdateTTL:d,noDeleteOnFetchRejection:u,allowStaleOnFetchRejection:m,allowStaleOnFetchAbort:C,ignoreFetchAbort:p,status:y,signal:_};let N=f(this,ft).get(t);if(N===void 0){y&&(y.fetch="miss");const D=A(this,I,Bs).call(this,t,N,w,x);return D.__returned=D}{const D=f(this,H)[N];if(A(this,I,nt).call(this,D)){const P=s&&D.__staleWhileFetching!==void 0;return y&&(y.fetch="inflight",P&&(y.returnedStale=!0)),P?D.__staleWhileFetching:D.__returned=D}const j=f(this,qt).call(this,N);if(!S&&!j)return y&&(y.fetch="hit"),A(this,I,ys).call(this,N),i&&f(this,We).call(this,N),y&&f(this,ce).call(this,y,N),D;const z=A(this,I,Bs).call(this,t,N,w,x),W=z.__staleWhileFetching!==void 0&&s;return y&&(y.fetch=j?"stale":"refresh",W&&j&&(y.returnedStale=!0)),W?z.__staleWhileFetching:z.__returned=z}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=f(this,os);if(!s)throw new Error("no memoMethod provided to constructor");const{context:i,forceRefresh:n,...o}=e,a=this.get(t,o);if(!n&&a!==void 0)return a;const c=s(t,a,{options:o,context:i});return this.set(t,c,o),c}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,status:o}=e,a=f(this,ft).get(t);if(a!==void 0){const c=f(this,H)[a],h=A(this,I,nt).call(this,c);return o&&f(this,ce).call(this,o,a),f(this,qt).call(this,a)?(o&&(o.get="stale"),h?(o&&s&&c.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?c.__staleWhileFetching:void 0):(n||A(this,I,be).call(this,t,"expire"),o&&s&&(o.returnedStale=!0),s?c:void 0)):(o&&(o.get="hit"),h?c.__staleWhileFetching:(A(this,I,ys).call(this,a),i&&f(this,We).call(this,a),c))}o&&(o.get="miss")}delete(t){return A(this,I,be).call(this,t,"delete")}clear(){return A(this,I,Wi).call(this,"delete")}};Vt=new WeakMap,Lt=new WeakMap,Bt=new WeakMap,Kt=new WeakMap,rs=new WeakMap,os=new WeakMap,$t=new WeakMap,Qt=new WeakMap,ft=new WeakMap,rt=new WeakMap,H=new WeakMap,At=new WeakMap,Rt=new WeakMap,Et=new WeakMap,bt=new WeakMap,Xt=new WeakMap,St=new WeakMap,Zt=new WeakMap,Jt=new WeakMap,Dt=new WeakMap,Yt=new WeakMap,we=new WeakMap,Ft=new WeakMap,I=new WeakSet,Pi=function(){const t=new Gs(f(this,Vt)),e=new Gs(f(this,Vt));R(this,Dt,t),R(this,Jt,e),R(this,bs,(n,o,a=Je.now())=>{if(e[n]=o!==0?a:0,t[n]=o,o!==0&&this.ttlAutopurge){const c=setTimeout(()=>{f(this,qt).call(this,n)&&A(this,I,be).call(this,f(this,rt)[n],"expire")},o+1);c.unref&&c.unref()}}),R(this,We,n=>{e[n]=t[n]!==0?Je.now():0}),R(this,ce,(n,o)=>{if(t[o]){const a=t[o],c=e[o];if(!a||!c)return;n.ttl=a,n.start=c,n.now=s||i();const h=n.now-c;n.remainingTTL=a-h}});let s=0;const i=()=>{const n=Je.now();if(this.ttlResolution>0){s=n;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return n};this.getRemainingTTL=n=>{const o=f(this,ft).get(n);if(o===void 0)return 0;const a=t[o],c=e[o];return!a||!c?1/0:a-((s||i())-c)},R(this,qt,n=>{const o=e[n],a=t[n];return!!a&&!!o&&(s||i())-o>a})},We=new WeakMap,ce=new WeakMap,bs=new WeakMap,qt=new WeakMap,Hr=function(){const t=new Gs(f(this,Vt));R(this,Qt,0),R(this,Zt,t),R(this,Ge,e=>{R(this,Qt,f(this,Qt)-t[e]),t[e]=0}),R(this,Ss,(e,s,i,n)=>{if(A(this,I,nt).call(this,s))return 0;if(!ve(i)){if(!n)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof n!="function")throw new TypeError("sizeCalculation must be a function");if(i=n(s,e),!ve(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return i}),R(this,as,(e,s,i)=>{if(t[e]=s,f(this,Lt)){const n=f(this,Lt)-t[e];for(;f(this,Qt)>n;)A(this,I,Vs).call(this,!0)}R(this,Qt,f(this,Qt)+t[e]),i&&(i.entrySize=s,i.totalCalculatedSize=f(this,Qt))})},Ge=new WeakMap,as=new WeakMap,Ss=new WeakMap,$e=function*({allowStale:t=this.allowStale}={}){if(f(this,$t))for(let e=f(this,bt);A(this,I,zi).call(this,e)&&(!t&&f(this,qt).call(this,e)||(yield e),e!==f(this,Et));)e=f(this,Rt)[e]},Ce=function*({allowStale:t=this.allowStale}={}){if(f(this,$t))for(let e=f(this,Et);A(this,I,zi).call(this,e)&&(!t&&f(this,qt).call(this,e)||(yield e),e!==f(this,bt));)e=f(this,At)[e]},zi=function(t){return t!==void 0&&f(this,ft).get(f(this,rt)[t])===t},Vs=function(t){var n,o;const e=f(this,Et),s=f(this,rt)[e],i=f(this,H)[e];return f(this,we)&&A(this,I,nt).call(this,i)?i.__abortController.abort(new Error("evicted")):(f(this,Yt)||f(this,Ft))&&(f(this,Yt)&&((n=f(this,Bt))==null||n.call(this,i,s,"evict")),f(this,Ft)&&((o=f(this,St))==null||o.push([i,s,"evict"]))),f(this,Ge).call(this,e),t&&(f(this,rt)[e]=void 0,f(this,H)[e]=void 0,f(this,Xt).push(e)),f(this,$t)===1?(R(this,Et,R(this,bt,0)),f(this,Xt).length=0):R(this,Et,f(this,At)[e]),f(this,ft).delete(s),Hs(this,$t)._--,e},Bs=function(t,e,s,i){const n=e===void 0?void 0:f(this,H)[e];if(A(this,I,nt).call(this,n))return n;const o=new ti,{signal:a}=s;a==null||a.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});const c={signal:o.signal,options:s,context:i},h=(p,C=!1)=>{const{aborted:x}=o.signal,S=s.ignoreFetchAbort&&p!==void 0;if(s.status&&(x&&!C?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,S&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),x&&!S&&!C)return d(o.signal.reason);const y=u;return f(this,H)[e]===u&&(p===void 0?y.__staleWhileFetching?f(this,H)[e]=y.__staleWhileFetching:A(this,I,be).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,p,c.options))),p},d=p=>{const{aborted:C}=o.signal,x=C&&s.allowStaleOnFetchAbort,S=x||s.allowStaleOnFetchRejection,y=S||s.noDeleteOnFetchRejection,_=u;if(f(this,H)[e]===u&&(!y||_.__staleWhileFetching===void 0?A(this,I,be).call(this,t,"fetch"):x||(f(this,H)[e]=_.__staleWhileFetching)),S)return s.status&&_.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),_.__staleWhileFetching;if(_.__returned===_)throw p};s.status&&(s.status.fetchDispatched=!0);const u=new Promise((p,C)=>{var S;const x=(S=f(this,rs))==null?void 0:S.call(this,t,n,c);x&&x instanceof Promise&&x.then(y=>p(y===void 0?void 0:y),C),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(p(void 0),s.allowStaleOnFetchAbort&&(p=y=>h(y,!0)))})}).then(h,p=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=p),d(p))),m=Object.assign(u,{__abortController:o,__staleWhileFetching:n,__returned:void 0});return e===void 0?(this.set(t,m,{...c.options,status:void 0}),e=f(this,ft).get(t)):f(this,H)[e]=m,m},nt=function(t){if(!f(this,we))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof ti},ji=function(t,e){f(this,Rt)[e]=t,f(this,At)[t]=e},ys=function(t){t!==f(this,bt)&&(t===f(this,Et)?R(this,Et,f(this,At)[t]):A(this,I,ji).call(this,f(this,Rt)[t],f(this,At)[t]),A(this,I,ji).call(this,f(this,bt),t),R(this,bt,t))},be=function(t,e){var i,n,o,a;let s=!1;if(f(this,$t)!==0){const c=f(this,ft).get(t);if(c!==void 0)if(s=!0,f(this,$t)===1)A(this,I,Wi).call(this,e);else{f(this,Ge).call(this,c);const h=f(this,H)[c];if(A(this,I,nt).call(this,h)?h.__abortController.abort(new Error("deleted")):(f(this,Yt)||f(this,Ft))&&(f(this,Yt)&&((i=f(this,Bt))==null||i.call(this,h,t,e)),f(this,Ft)&&((n=f(this,St))==null||n.push([h,t,e]))),f(this,ft).delete(t),f(this,rt)[c]=void 0,f(this,H)[c]=void 0,c===f(this,bt))R(this,bt,f(this,Rt)[c]);else if(c===f(this,Et))R(this,Et,f(this,At)[c]);else{const d=f(this,Rt)[c];f(this,At)[d]=f(this,At)[c];const u=f(this,At)[c];f(this,Rt)[u]=f(this,Rt)[c]}Hs(this,$t)._--,f(this,Xt).push(c)}}if(f(this,Ft)&&((o=f(this,St))!=null&&o.length)){const c=f(this,St);let h;for(;h=c==null?void 0:c.shift();)(a=f(this,Kt))==null||a.call(this,...h)}return s},Wi=function(t){var e,s,i;for(const n of A(this,I,Ce).call(this,{allowStale:!0})){const o=f(this,H)[n];if(A(this,I,nt).call(this,o))o.__abortController.abort(new Error("deleted"));else{const a=f(this,rt)[n];f(this,Yt)&&((e=f(this,Bt))==null||e.call(this,o,a,t)),f(this,Ft)&&((s=f(this,St))==null||s.push([o,a,t]))}}if(f(this,ft).clear(),f(this,H).fill(void 0),f(this,rt).fill(void 0),f(this,Dt)&&f(this,Jt)&&(f(this,Dt).fill(0),f(this,Jt).fill(0)),f(this,Zt)&&f(this,Zt).fill(0),R(this,Et,0),R(this,bt,0),f(this,Xt).length=0,R(this,Qt,0),R(this,$t,0),f(this,Ft)&&f(this,St)){const n=f(this,St);let o;for(;o=n==null?void 0:n.shift();)(i=f(this,Kt))==null||i.call(this,...o)}};let Hi=rn;class Pr{constructor(){l(this,"_syncStatus",{status:ea.done,foldersProgress:[]});l(this,"_syncEnabledState",Rn.initializing);l(this,"_workspaceGuidelines",[]);l(this,"_openUserGuidelinesInput",!1);l(this,"_userGuidelines");l(this,"_contextStore",new Da);l(this,"_prevOpenFiles",[]);l(this,"_disableContext",!1);l(this,"_enableAgentMemories",!1);l(this,"subscribers",new Set);l(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));l(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case lt.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case lt.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case lt.fileRangesSelected:this.updateSelections(e.data);break;case lt.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case lt.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case lt.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});l(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:Ct.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});l(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});l(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});l(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});l(this,"addFile",t=>{this.addFiles([t])});l(this,"addFiles",t=>{this.updateFiles(t,[])});l(this,"removeFile",t=>{this.removeFiles([t])});l(this,"removeFiles",t=>{this.updateFiles([],t)});l(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});l(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});l(this,"updateFiles",(t,e)=>{const s=o=>({file:o,...yi(o)}),i=t.map(s),n=e.map(s);this._contextStore.update(i,n,o=>o.id),this.notifySubscribers()});l(this,"updateRules",(t,e)=>{const s=o=>({rule:o,...Qo(o)}),i=t.map(s),n=e.map(s);this._contextStore.update(i,n,o=>o.id),this.notifySubscribers()});l(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});l(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});l(this,"setCurrentlyOpenFiles",t=>{const e=t.map(i=>({recentFile:i,...yi(i)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,i=>i.id),s.forEach(i=>{const n=this._contextStore.peekKey(i.id);n!=null&&n.recentFile&&(n.file=n.recentFile,delete n.recentFile)}),e.forEach(i=>{const n=this._contextStore.peekKey(i.id);n!=null&&n.file&&(n.recentFile=n.file,delete n.file)}),this.notifySubscribers()});l(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});l(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,i=t.overLimit||((e==null?void 0:e.overLimit)??!1),n={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:Ct.active,referenceCount:1,showWarning:i,rulesAndGuidelinesState:e};this._contextStore.update([n],s,o=>{var a;return o.id+String((a=o.userGuidelines)==null?void 0:a.overLimit)}),this.notifySubscribers()});l(this,"onGuidelinesStateUpdate",t=>{var i;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const n=e||{overLimit:!1,contents:"",lengthLimit:((i=t.rulesAndGuidelines)==null?void 0:i.lengthLimit)??2e3};this.updateUserGuidelines(n,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(n=>n.sourceFolder))});l(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(i=>i.workspaceFolder===e.folderRoot);return{...e,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));l(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});l(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});l(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});l(this,"updateSelections",t=>{const e=this._contextStore.values.filter(Xs),s=t.map(i=>({selection:i,...yi(i)}));this._contextStore.update([],e,i=>i.id),this._contextStore.update(s,[],i=>i.id),this.notifySubscribers()});l(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});l(this,"markInactive",t=>{this.markItemsInactive([t])});l(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,Ct.inactive)}),this.notifySubscribers()});l(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});l(this,"markActive",t=>{this.markItemsActive([t])});l(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,Ct.active)}),this.notifySubscribers()});l(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});l(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});l(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});l(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Ji(t)&&!Qs(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Qs)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(Xs)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Yi)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(Zs)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(tn)}get userGuidelines(){return this._contextStore.values.filter(Js)}get agentMemories(){return[{...Ko,status:this._enableAgentMemories?Ct.active:Ct.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>Ys(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===Ct.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===Ct.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===Ct.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===Ct.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===Ct.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===Ct.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var c;if(this.syncEnabledState===Rn.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(h=>h.progress!==void 0);if(t.length===0)return;const e=t.reduce((h,d)=>{var u;return h+(((u=d==null?void 0:d.progress)==null?void 0:u.trackedFiles)??0)},0),s=t.reduce((h,d)=>{var u;return h+(((u=d==null?void 0:d.progress)==null?void 0:u.backlogSize)??0)},0),i=Math.max(e,0),n=Math.min(Math.max(s,0),i),o=i-n,a=[];for(const h of t)(c=h==null?void 0:h.progress)!=null&&c.newlyTracked&&a.push(h.folderRoot);return{status:this._syncStatus.status,totalFiles:i,syncedCount:o,backlogSize:n,newlyTrackedFolders:a}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(Zs(t)||Js(t)||ii(t)||Ys(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===Ct.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===Ct.inactive)}get isContextDisabled(){return this._disableContext}}class Da{constructor(){l(this,"_cache",new Hi({max:1e3}));l(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));l(this,"clear",()=>{this._cache.clear()});l(this,"update",(t,e,s)=>{t.forEach(i=>this.addInPlace(i,s)),e.forEach(i=>this.removeInPlace(i,s))});l(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});l(this,"addInPlace",(t,e)=>{const s=e(t),i=t.referenceCount??1,n=this._cache.get(s),o=t.status??(n==null?void 0:n.status)??Ct.active;n?(n.referenceCount+=i,n.status=o,n.pinned=t.pinned??n.pinned,n.showWarning=t.showWarning??n.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in n&&(n.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in n&&(n.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:i,status:o})});l(this,"removeInPlace",(t,e)=>{const s=e(t),i=this._cache.get(s);i&&(i.referenceCount-=1,i.referenceCount===0&&this._cache.delete(s))});l(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});l(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});l(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});l(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});l(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===Ct.active?Ct.inactive:Ct.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class qa{constructor(t,e,s){l(this,"_originalModel");l(this,"_modifiedModel");l(this,"_fullEdits",[]);l(this,"_currEdit");l(this,"_currOriginalEdit");l(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(e=>{this._modifiedModel.applyEdits([e])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});l(this,"finish",()=>this._completeCurrEdit());l(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);l(this,"_completeCurrEdit",t=>{const e={resetOriginal:[],original:[],modified:[]};if(!t)return e;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const s=this._nextModifiedInsertPosition(),i=t.stagedEndLine-t.stagedStartLine,n={range:new this._monaco.Range(s.lineNumber,0,s.lineNumber+i,0),text:""};e.modified.push(n),this._modifiedModel.applyEdits([n]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return e});l(this,"_startNewEdit",t=>{const e={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},e.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),e});l(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const e=this._nextModifiedInsertPosition(),s={...this._currEdit,text:t.newText,range:new this._monaco.Range(e.lineNumber,e.column,e.lineNumber,e.column)};return this._modifiedModel.applyEdits([s]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[s]:[]}});l(this,"_nextModifiedInsertPosition",()=>{var e;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((e=this._currEdit.text)==null?void 0:e.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=e,this._monaco=s,this._originalModel=this._monaco.editor.createModel(e),this._modifiedModel=this._monaco.editor.createModel(e)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class Ua{constructor(t,e,s){l(this,"_asyncMsgSender");l(this,"_editor");l(this,"_chatModel");l(this,"_focusModel",new kr);l(this,"_hasScrolledOnInit",!1);l(this,"_markHasScrolledOnInit",ki(()=>{this._hasScrolledOnInit=!0},200));l(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});l(this,"_subscribers",new Set);l(this,"_disposables",[]);l(this,"_rootChunk");l(this,"_keybindings",ut({}));l(this,"_requestId",ut(void 0));l(this,"requestId",this._requestId);l(this,"_disableResolution",ut(!1));l(this,"disableResolution",An(this._disableResolution));l(this,"_disableApply",ut(!1));l(this,"disableApply",An(this._disableApply));l(this,"_currStream");l(this,"_isLoadingDiffChunks",ut(!1));l(this,"_selectionLines",ut(void 0));l(this,"_mode",ut(Te.edit));l(this,"initializeEditor",t=>{var e,s,i,n,o,a,c,h,d,u,m,p;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new en(new Ar(vs),vs,new Pr),(s=(e=this._monaco.editor).registerCommand)==null||s.call(e,"acceptFocusedChunk",this.acceptFocusedChunk),(n=(i=this._monaco.editor).registerCommand)==null||n.call(i,"rejectFocusedChunk",this.rejectFocusedChunk),(a=(o=this._monaco.editor).registerCommand)==null||a.call(o,"acceptAllChunks",this.acceptAllChunks),(h=(c=this._monaco.editor).registerCommand)==null||h.call(c,"rejectAllChunks",this.rejectAllChunks),(u=(d=this._monaco.editor).registerCommand)==null||u.call(d,"focusNextChunk",this.focusNextChunk),(p=(m=this._monaco.editor).registerCommand)==null||p.call(m,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(C=>this.notifySubscribers())}),this.initialize()});l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});l(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});l(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const e=this.leaves[0];this.revealChunk(e)}this.notifyDiffViewUpdated(),this.notifySubscribers()});l(this,"onMouseMoveModified",t=>{var i,n,o,a,c,h;if(((i=t.target.position)==null?void 0:i.lineNumber)===void 0||this.leaves===void 0)return;const e=this.editorOffset,s=(n=t.target.position)==null?void 0:n.lineNumber;for(let d=0;d<this.leaves.length;d++){const u=this.leaves[d],m=(o=u.unitOfCodeWork.lineChanges)==null?void 0:o.lineChanges[0].modifiedStart,p=(a=u.unitOfCodeWork.lineChanges)==null?void 0:a.lineChanges[0].modifiedEnd,C=(c=u.unitOfCodeWork.lineChanges)==null?void 0:c.lineChanges[0].originalStart,x=(h=u.unitOfCodeWork.lineChanges)==null?void 0:h.lineChanges[0].originalEnd;if(m!==void 0&&p!==void 0&&C!==void 0&&x!==void 0){if(m!==p||s!==m){if(m<=s&&s<p){this.setCurrFocusedChunkIdx(d,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const S=this._editor.getOriginalEditor(),y=S.getOption(this._monaco.editor.EditorOption.lineHeight),_=S.getScrolledVisiblePosition({lineNumber:C,column:0}),w=S.getScrolledVisiblePosition({lineNumber:x+1,column:0});if(_===null||w===null)continue;const N=_.top-y/2+e,D=w.top-y/2+e;if(t.event.posy>=N&&t.event.posy<=D){this.setCurrFocusedChunkIdx(d,!1);break}break}}}});l(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:lt.diffViewWindowFocusChange,data:t})});l(this,"setCurrFocusedChunkIdx",(t,e=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),e&&this.revealCurrFocusedChunk(),this.notifySubscribers())});l(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});l(this,"revealChunk",t=>{var i;const e=(i=t.unitOfCodeWork.lineChanges)==null?void 0:i.lineChanges[0],s=e==null?void 0:e.modifiedStart;s!==void 0&&this._editor.revealLineNearTop(s-1)});l(this,"renderCentralOverlayWidget",t=>{const e=()=>({editor:this._editor,id:"central-overlay-widget"}),s=function(i,n,o){let a,c=n;const h=()=>c.editor.getModifiedEditor(),d=()=>{const u=h();if(!u)return;const m={getDomNode:()=>i,getId:()=>c.id,getPosition:()=>({preference:o.monaco.editor.OverlayWidgetPositionPreference.TOP_CENTER})};a&&u.removeOverlayWidget(a),u.addOverlayWidget(m),a=m};return d(),{update:u=>{c=u,d()},destroy:()=>{const u=h();u&&a&&u.removeOverlayWidget(a)}}}(t,e(),{monaco:this._monaco});return{update:()=>{s.update(e())},destroy:s.destroy}});l(this,"renderInstructionsDrawerViewZone",(t,e)=>{let s=!1,i=e;const n=e.autoFocus??!0,o=d=>{n&&!s&&(this._editor.revealLineNearTop(d),s=!0)},a=d=>({...d,ordinal:Di.instructionDrawer,editor:this._editor,afterLineNumber:d.line}),c=Wn(t,a(e)),h=[];return n&&h.push(this._editor.onDidUpdateDiff(()=>{o(i.line)})),{update:d=>{const u={...i,...d};wa(u,i)||(c.update(a(u)),i=u,o(u.line))},destroy:()=>{c.destroy(),h.forEach(d=>d.dispose())}}});l(this,"renderActionsViewZone",(t,e)=>{const s=n=>{var a;let o;return o=n.chunk?(a=n.chunk.unitOfCodeWork.lineChanges)==null?void 0:a.lineChanges[0].modifiedStart:1,{...n,ordinal:Di.chunkActionPanel,editor:this._editor,afterLineNumber:o?o-1:void 0}},i=Wn(t,s(e));return{update:n=>{i.update(s(n))},destroy:i.destroy}});l(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});l(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});l(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});l(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});l(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});l(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});l(this,"initialize",async()=>{var c;const t=await this._asyncMsgSender.send({type:lt.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:e,instruction:s,keybindings:i,editable:n}=t.data;this._editor.updateOptions({readOnly:!n});const o=It(this._keybindings);this._keybindings.set(i??o);const a=s==null?void 0:s.selection;a&&(a.start.line===a.end.line&&a.start.character===a.end.character&&this._mode.set(Te.instruction),It(this.selectionLines)===void 0&&this._selectionLines.set({start:a.start.line,end:a.end.line})),this.updateModels(e.originalCode??"",e.modifiedCode??"",{rootPath:e.repoRoot,relPath:e.pathName}),(c=this._currStream)==null||c.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});l(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:lt.disposeDiffView})});l(this,"_tryFetchStream",async()=>{var e,s,i;const t=this._asyncMsgSender.stream({type:lt.diffViewFetchPendingStream},15e3,6e4);for await(const n of t)switch(n.type){case lt.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(n.data.requestId);const o=this._editor.getOriginalEditor().getValue();this._currStream=new qa(n.data.streamId,o,this._monaco),this._syncStreamToModels();break}case lt.diffViewDiffStreamEnded:if(((e=this._currStream)==null?void 0:e.id)!==n.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case lt.diffViewDiffStreamChunk:{if(((s=this._currStream)==null?void 0:s.id)!==n.data.streamId)return;const o=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!o)return this.setLoading(!1),void this._cleanupStream();const a=(i=this._currStream)==null?void 0:i.onReceiveChunk(n);a&&(this._applyDeltaDiff(a),It(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});l(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case lt.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case lt.diffViewAcceptAllChunks:this.acceptAllChunks();break;case lt.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case lt.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case lt.diffViewFocusPrevChunk:this.focusPrevChunk();break;case lt.diffViewFocusNextChunk:this.focusNextChunk()}});l(this,"_applyDeltaDiff",t=>{const e=this._editor.getOriginalEditor().getModel(),s=this._editor.getModifiedEditor().getModel();e&&s&&(e.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(i=>{e.pushEditOperations([],[i],()=>[])}),t.modified.forEach(i=>{s.pushEditOperations([],[i],()=>[])}))});l(this,"_cleanupStream",()=>{var t;if(this._currStream){const e=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(e),this._currStream=void 0,this._resetScrollOnInit()}});l(this,"_syncStreamToModels",()=>{var s,i;const t=(s=this._currStream)==null?void 0:s.originalValue,e=(i=this._currStream)==null?void 0:i.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),e&&e!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(e)});l(this,"acceptChunk",async t=>{It(this._disableApply)||this.acceptChunks([t])});l(this,"acceptChunks",async(t,e=!1)=>{It(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,gi.accept,e),await Ei(),this.areModelsEqual()&&!It(this.isLoading)&&this.disposeDiffViewPanel())});l(this,"areModelsEqual",()=>{var s,i;const t=(s=this._editor.getModel())==null?void 0:s.original,e=(i=this._editor.getModel())==null?void 0:i.modified;return(t==null?void 0:t.getValue())===(e==null?void 0:e.getValue())});l(this,"rejectChunk",async t=>{this.rejectChunks([t])});l(this,"rejectChunks",async(t,e=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,gi.reject,e),await Ei(),this.areModelsEqual()&&!It(this.isLoading)&&this.disposeDiffViewPanel()});l(this,"notifyDiffViewUpdated",ki(()=>{this.notifyResolvedChunks([],gi.accept)},1e3));l(this,"notifyResolvedChunks",async(t,e,s=!1)=>{var n;const i=(n=this._editor.getModel())==null?void 0:n.original.uri.path;i&&await this._asyncMsgSender.send({type:lt.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:i,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(o=>o.unitOfCodeWork),resolveType:e,shouldApplyToAll:s}},2e3)});l(this,"executeDiffChunks",(t,e)=>{var d,u,m;if(It(this._disableResolution)||e&&It(this._disableApply))return;const s=(d=this._editor.getModel())==null?void 0:d.original,i=(u=this._editor.getModel())==null?void 0:u.modified;if(!s||!i||this._currStream!==void 0)return;const n=[],o=[];for(const p of t){const C=(m=p.unitOfCodeWork.lineChanges)==null?void 0:m.lineChanges[0];if(!C||p.unitOfCodeWork.originalCode===void 0||p.unitOfCodeWork.modifiedCode===void 0)continue;let x={startLineNumber:C.originalStart,startColumn:1,endLineNumber:C.originalEnd,endColumn:1},S={startLineNumber:C.modifiedStart,startColumn:1,endLineNumber:C.modifiedEnd,endColumn:1};const y=e?p.unitOfCodeWork.modifiedCode:p.unitOfCodeWork.originalCode;y!==void 0&&(n.push({range:x,text:y}),o.push({range:S,text:y}))}s.pushEditOperations([],n,()=>[]),i.pushEditOperations([],o,()=>[]);const a=this._focusModel.nextIdx({nowrap:!0});if(a===void 0)return;const c=a===this._focusModel.focusedItemIdx?a-1:a,h=this._focusModel.items[c];h&&this.revealChunk(h)});l(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});l(this,"handleInstructionSubmit",t=>{const e=this._editor.getModifiedEditor(),s=this.getSelectedCodeDetails(e);if(!s)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,s)});l(this,"updateModels",(t,e,s)=>{var o,a;const i=(a=(o=this._editor.getModel())==null?void 0:o.original)==null?void 0:a.uri,n=(s&&this._monaco.Uri.file(s.relPath))??i;if(n)if((i==null?void 0:i.fsPath)!==n.fsPath||(i==null?void 0:i.authority)!==n.authority){const c=n.with({fragment:crypto.randomUUID()}),h=n.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,c),modified:this._monaco.editor.createModel(e??"",void 0,h)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==e&&this.getModifiedEditor().setValue(e??"");else console.warn("No URI found for diff view. Not updating models.")});l(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=s,this._asyncMsgSender=new Po(i=>vs.postMessage(i)),this.initializeEditor(e)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return Rr(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var n,o;const t=[],e=this._editor.getLineChanges(),s=(n=this._editor.getModel())==null?void 0:n.original,i=(o=this._editor.getModel())==null?void 0:o.modified;if(e&&s&&i){for(const a of e){const c=Bn({startLineNumber:a.originalStartLineNumber,startColumn:1,endLineNumber:a.originalEndLineNumber,endColumn:1}),h=Bn({startLineNumber:a.modifiedStartLineNumber,startColumn:1,endLineNumber:a.modifiedEndLineNumber,endColumn:1}),d=Ha(this._editor,c,h);t.push(d)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(a=>a.id)}}}getSelectedCodeDetails(t){const e=t.getModel();if(!e)return null;const s=e.getLanguageId(),i=1,n=1,o={lineNumber:e.getLineCount(),column:e.getLineMaxColumn(e.getLineCount())},a=It(this._selectionLines);if(!a)throw new Error("No selection lines found");const c=Math.min(a.end+1,o.lineNumber),h=new this._monaco.Range(a.start+1,1,c,e.getLineMaxColumn(c));let d=e.getValueInRange(h);c<e.getLineCount()&&(d+=e.getEOL());const u=new this._monaco.Range(i,n,h.startLineNumber,h.startColumn),m=Math.min(h.endLineNumber+1,o.lineNumber),p=new this._monaco.Range(m,1,o.lineNumber,o.column);return{selectedCode:d,prefix:e.getValueInRange(u),suffix:e.getValueInRange(p),path:e.uri.path,language:s,prefixBegin:u.startLineNumber-1,suffixEnd:p.endLineNumber-1}}}function Ha(r,t,e){var n,o;const s=(n=r.getModel())==null?void 0:n.original,i=(o=r.getModel())==null?void 0:o.modified;if(!s||!i)throw new Error("No models found");return function(a,c,h,d){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:a,modifiedCode:c,lineChanges:{lineChanges:[{originalStart:h.startLineNumber,originalEnd:h.endLineNumber,modifiedStart:d.startLineNumber,modifiedEnd:d.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(s.getValueInRange(t),i.getValueInRange(e),t,e)}function Bn(r){return r.endLineNumber===0?{startLineNumber:r.startLineNumber+1,startColumn:1,endLineNumber:r.startLineNumber+1,endColumn:1}:{startLineNumber:r.startLineNumber,startColumn:1,endLineNumber:r.endLineNumber+1,endColumn:1}}function Kn(r){let t,e;return t=new Ee({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[Pa]},$$scope:{ctx:r}}}),t.$on("click",function(){de(r[4])&&r[4].apply(this,arguments)}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){r=s;const n={};132096&i&&(n.$$scope={dirty:i,ctx:r}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Pa(r){let t,e,s;return t=new Be({props:{keybinding:r[10].acceptFocusedChunk}}),{c(){E(t.$$.fragment),e=ct(`
        Accept`)},m(i,n){T(t,i,n),O(i,e,n),s=!0},p(i,n){const o={};1024&n&&(o.keybinding=i[10].acceptFocusedChunk),t.$set(o)},i(i){s||($(t.$$.fragment,i),s=!0)},o(i){b(t.$$.fragment,i),s=!1},d(i){i&&F(e),k(t,i)}}}function za(r){let t,e,s;return t=new Be({props:{keybinding:r[10].rejectFocusedChunk}}),{c(){E(t.$$.fragment),e=ct(`
      Reject`)},m(i,n){T(t,i,n),O(i,e,n),s=!0},p(i,n){const o={};1024&n&&(o.keybinding=i[10].rejectFocusedChunk),t.$set(o)},i(i){s||($(t.$$.fragment,i),s=!0)},o(i){b(t.$$.fragment,i),s=!1},d(i){i&&F(e),k(t,i)}}}function ja(r){let t,e,s,i,n,o,a,c,h,d,u=!r[3]&&Kn(r);return a=new Ee({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[za]},$$scope:{ctx:r}}}),a.$on("click",function(){de(r[5])&&r[5].apply(this,arguments)}),{c(){t=et("div"),s=J(),i=et("div"),n=et("div"),u&&u.c(),o=J(),E(a.$$.fragment),X(t,"class","svelte-zm1705"),jt(t,"c-chunk-diff-border--focused",!!r[7]&&r[1]),X(n,"class","c-button-container svelte-zm1705"),jt(n,"c-button-container--focused",r[1]),jt(n,"c-button-container--transparent",r[9]),X(i,"class","c-chunk-action-panel-anchor svelte-zm1705"),ts(i,"top",r[8]+"px"),jt(i,"c-chunk-action-panel-anchor--left",r[0]==="left"),jt(i,"c-chunk-action-panel-anchor--right",r[0]==="right"),jt(i,"c-chunk-action-panel-anchor--focused",r[1])},m(m,p){O(m,t,p),O(m,s,p),O(m,i,p),st(i,n),u&&u.m(n,null),st(n,o),T(a,n,null),c=!0,h||(d=[Cr(e=r[6].renderActionsViewZone(t,{chunk:r[7],heightInPx:r[2],onDomNodeTop:r[12]})),Ie(i,"mouseenter",r[13]),Ie(i,"mousemove",r[13]),Ie(i,"mouseleave",r[13])],h=!0)},p(m,[p]){r=m,e&&de(e.update)&&132&p&&e.update.call(null,{chunk:r[7],heightInPx:r[2],onDomNodeTop:r[12]}),(!c||130&p)&&jt(t,"c-chunk-diff-border--focused",!!r[7]&&r[1]),r[3]?u&&(gt(),b(u,1,1,()=>{u=null}),_t()):u?(u.p(r,p),8&p&&$(u,1)):(u=Kn(r),u.c(),$(u,1),u.m(n,o));const C={};132096&p&&(C.$$scope={dirty:p,ctx:r}),a.$set(C),(!c||2&p)&&jt(n,"c-button-container--focused",r[1]),(!c||512&p)&&jt(n,"c-button-container--transparent",r[9]),(!c||256&p)&&ts(i,"top",r[8]+"px"),(!c||1&p)&&jt(i,"c-chunk-action-panel-anchor--left",r[0]==="left"),(!c||1&p)&&jt(i,"c-chunk-action-panel-anchor--right",r[0]==="right"),(!c||2&p)&&jt(i,"c-chunk-action-panel-anchor--focused",r[1])},i(m){c||($(u),$(a.$$.fragment,m),c=!0)},o(m){b(u),b(a.$$.fragment,m),c=!1},d(m){m&&(F(t),F(s),F(i)),u&&u.d(),k(a),h=!1,Xi(d)}}}function Wa(r,t,e){let s,{align:i="right"}=t,{isFocused:n}=t,{heightInPx:o=1}=t,{disableApply:a=!1}=t,{onAccept:c}=t,{onReject:h}=t,{diffViewModel:d}=t,{leaf:u}=t;const m=d.keybindings;Ve(r,m,y=>e(10,s=y));let p=0,C,x=!1;function S(){C&&(clearTimeout(C),C=void 0),e(9,x=!1)}return r.$$set=y=>{"align"in y&&e(0,i=y.align),"isFocused"in y&&e(1,n=y.isFocused),"heightInPx"in y&&e(2,o=y.heightInPx),"disableApply"in y&&e(3,a=y.disableApply),"onAccept"in y&&e(4,c=y.onAccept),"onReject"in y&&e(5,h=y.onReject),"diffViewModel"in y&&e(6,d=y.diffViewModel),"leaf"in y&&e(7,u=y.leaf)},[i,n,o,a,c,h,d,u,p,x,s,m,y=>{e(8,p=y)},function(y){y.target.closest(".c-button-container")?S():y.type==="mouseenter"||y.type==="mousemove"?(S(),C=setTimeout(()=>{e(9,x=!0)},400)):y.type==="mouseleave"&&S()}]}class Ga extends ke{constructor(t){super(),Ae(this,t,Wa,ja,Fe,{align:0,isFocused:1,heightInPx:2,disableApply:3,onAccept:4,onReject:5,diffViewModel:6,leaf:7})}}function Qn(r){let t,e,s;function i(o){r[18](o)}let n={onOpenChange:r[16],content:r[3],triggerOn:[ca.Hover],$$slots:{default:[Ba]},$$scope:{ctx:r}};return r[4]!==void 0&&(n.requestClose=r[4]),t=new la({props:n}),ss.push(()=>wo(t,"requestClose",i)),{c(){E(t.$$.fragment)},m(o,a){T(t,o,a),s=!0},p(o,a){const c={};8&a&&(c.content=o[3]),1048576&a&&(c.$$scope={dirty:a,ctx:o}),!e&&16&a&&(e=!0,c.requestClose=o[4],Mo(()=>e=!1)),t.$set(c)},i(o){s||($(t.$$.fragment,o),s=!0)},o(o){b(t.$$.fragment,o),s=!1},d(o){k(t,o)}}}function Va(r){let t,e;return t=new oa({}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Ba(r){let t,e;return t=new Uo({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Va]},$$scope:{ctx:r}}}),t.$on("click",r[17]),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};1048576&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Ka(r){let t;return{c(){t=et("span"),t.textContent="No changes",X(t,"class","c-diff-page-counter svelte-1w94ymh")},m(e,s){O(e,t,s)},p:Z,i:Z,o:Z,d(e){e&&F(t)}}}function Qa(r){var h,d;let t,e,s,i,n,o,a,c=((d=(h=r[1])==null?void 0:h.leaves)==null?void 0:d.length)+"";return o=new br({props:{size:1,loading:r[10]}}),{c(){t=et("span"),e=ct(r[2]),s=ct(" of "),i=ct(c),n=J(),E(o.$$.fragment),X(t,"class","c-diff-page-counter svelte-1w94ymh")},m(u,m){O(u,t,m),st(t,e),st(t,s),st(t,i),st(t,n),T(o,t,null),a=!0},p(u,m){var C,x;(!a||4&m)&&Ht(e,u[2]),(!a||2&m)&&c!==(c=((x=(C=u[1])==null?void 0:C.leaves)==null?void 0:x.length)+"")&&Ht(i,c);const p={};1024&m&&(p.loading=u[10]),o.$set(p)},i(u){a||($(o.$$.fragment,u),a=!0)},o(u){b(o.$$.fragment,u),a=!1},d(u){u&&F(t),k(o)}}}function Xa(r){let t,e,s,i;return s=new br({props:{size:1,loading:r[10]}}),{c(){t=et("span"),e=ct(`Generating changes
        `),E(s.$$.fragment),X(t,"class","c-diff-page-counter svelte-1w94ymh")},m(n,o){O(n,t,o),st(t,e),T(s,t,null),i=!0},p(n,o){const a={};1024&o&&(a.loading=n[10]),s.$set(a)},i(n){i||($(s.$$.fragment,n),i=!0)},o(n){b(s.$$.fragment,n),i=!1},d(n){n&&F(t),k(s)}}}function Xn(r){let t,e,s,i,n,o;t=new Ee({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Za]},$$scope:{ctx:r}}}),t.$on("click",function(){de(r[0].focusPrevChunk)&&r[0].focusPrevChunk.apply(this,arguments)}),s=new Ee({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Ja]},$$scope:{ctx:r}}}),s.$on("click",function(){de(r[0].focusNextChunk)&&r[0].focusNextChunk.apply(this,arguments)});let a=!r[12]&&Zn(r);return{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment),i=J(),a&&a.c(),n=ie()},m(c,h){T(t,c,h),O(c,e,h),T(s,c,h),O(c,i,h),a&&a.m(c,h),O(c,n,h),o=!0},p(c,h){r=c;const d={};1050624&h&&(d.$$scope={dirty:h,ctx:r}),t.$set(d);const u={};1050624&h&&(u.$$scope={dirty:h,ctx:r}),s.$set(u),r[12]?a&&(gt(),b(a,1,1,()=>{a=null}),_t()):a?(a.p(r,h),4096&h&&$(a,1)):(a=Zn(r),a.c(),$(a,1),a.m(n.parentNode,n))},i(c){o||($(t.$$.fragment,c),$(s.$$.fragment,c),$(a),o=!0)},o(c){b(t.$$.fragment,c),b(s.$$.fragment,c),b(a),o=!1},d(c){c&&(F(e),F(i),F(n)),k(t,c),k(s,c),a&&a.d(c)}}}function Za(r){let t,e,s;return t=new Be({props:{keybinding:r[11].focusPrevChunk}}),{c(){E(t.$$.fragment),e=ct(`
        Back`)},m(i,n){T(t,i,n),O(i,e,n),s=!0},p(i,n){const o={};2048&n&&(o.keybinding=i[11].focusPrevChunk),t.$set(o)},i(i){s||($(t.$$.fragment,i),s=!0)},o(i){b(t.$$.fragment,i),s=!1},d(i){i&&F(e),k(t,i)}}}function Ja(r){let t,e,s;return t=new Be({props:{keybinding:r[11].focusNextChunk}}),{c(){E(t.$$.fragment),e=ct(`
        Next`)},m(i,n){T(t,i,n),O(i,e,n),s=!0},p(i,n){const o={};2048&n&&(o.keybinding=i[11].focusNextChunk),t.$set(o)},i(i){s||($(t.$$.fragment,i),s=!0)},o(i){b(t.$$.fragment,i),s=!1},d(i){i&&F(e),k(t,i)}}}function Zn(r){let t,e,s,i=!r[13]&&Jn(r);return e=new Ee({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[tc]},$$scope:{ctx:r}}}),e.$on("click",function(){de(r[0].rejectAllChunks)&&r[0].rejectAllChunks.apply(this,arguments)}),{c(){i&&i.c(),t=J(),E(e.$$.fragment)},m(n,o){i&&i.m(n,o),O(n,t,o),T(e,n,o),s=!0},p(n,o){(r=n)[13]?i&&(gt(),b(i,1,1,()=>{i=null}),_t()):i?(i.p(r,o),8192&o&&$(i,1)):(i=Jn(r),i.c(),$(i,1),i.m(t.parentNode,t));const a={};1050624&o&&(a.$$scope={dirty:o,ctx:r}),e.$set(a)},i(n){s||($(i),$(e.$$.fragment,n),s=!0)},o(n){b(i),b(e.$$.fragment,n),s=!1},d(n){n&&F(t),i&&i.d(n),k(e,n)}}}function Jn(r){let t,e;return t=new Ee({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[Ya]},$$scope:{ctx:r}}}),t.$on("click",function(){de(r[0].acceptAllChunks)&&r[0].acceptAllChunks.apply(this,arguments)}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){r=s;const n={};1050624&i&&(n.$$scope={dirty:i,ctx:r}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Ya(r){let t,e,s;return t=new Be({props:{keybinding:r[11].acceptAllChunks}}),{c(){E(t.$$.fragment),e=ct(`
            Accept All`)},m(i,n){T(t,i,n),O(i,e,n),s=!0},p(i,n){const o={};2048&n&&(o.keybinding=i[11].acceptAllChunks),t.$set(o)},i(i){s||($(t.$$.fragment,i),s=!0)},o(i){b(t.$$.fragment,i),s=!1},d(i){i&&F(e),k(t,i)}}}function tc(r){let t,e,s;return t=new Be({props:{keybinding:r[11].rejectAllChunks}}),{c(){E(t.$$.fragment),e=ct(`
          Reject All`)},m(i,n){T(t,i,n),O(i,e,n),s=!0},p(i,n){const o={};2048&n&&(o.keybinding=i[11].rejectAllChunks),t.$set(o)},i(i){s||($(t.$$.fragment,i),s=!0)},o(i){b(t.$$.fragment,i),s=!1},d(i){i&&F(e),k(t,i)}}}function ec(r){let t,e,s,i,n,o,a,c=r[9]&&Qn(r);const h=[Xa,Qa,Ka],d=[];function u(p,C){return!p[5]&&p[10]?0:p[5]?1:2}i=u(r),n=d[i]=h[i](r);let m=r[5]&&Xn(r);return{c(){t=et("div"),e=et("div"),c&&c.c(),s=J(),n.c(),o=J(),m&&m.c(),X(e,"class","c-button-container svelte-1w94ymh"),X(t,"class","c-top-action-panel-anchor svelte-1w94ymh")},m(p,C){O(p,t,C),st(t,e),c&&c.m(e,null),st(e,s),d[i].m(e,null),st(e,o),m&&m.m(e,null),a=!0},p(p,[C]){p[9]?c?(c.p(p,C),512&C&&$(c,1)):(c=Qn(p),c.c(),$(c,1),c.m(e,s)):c&&(gt(),b(c,1,1,()=>{c=null}),_t());let x=i;i=u(p),i===x?d[i].p(p,C):(gt(),b(d[x],1,1,()=>{d[x]=null}),_t(),n=d[i],n?n.p(p,C):(n=d[i]=h[i](p),n.c()),$(n,1),n.m(e,o)),p[5]?m?(m.p(p,C),32&C&&$(m,1)):(m=Xn(p),m.c(),$(m,1),m.m(e,null)):m&&(gt(),b(m,1,1,()=>{m=null}),_t())},i(p){a||($(c),$(n),$(m),a=!0)},o(p){b(c),b(n),b(m),a=!1},d(p){p&&F(t),c&&c.d(),d[i].d(),m&&m.d()}}}function sc(r,t,e){let s,i,n,o,a,c,h,d,u,m,p=Z,C=()=>(p(),p=he(_,P=>e(1,c=P)),_),x=Z,S=Z,y=Z;r.$$.on_destroy.push(()=>p()),r.$$.on_destroy.push(()=>x()),r.$$.on_destroy.push(()=>S()),r.$$.on_destroy.push(()=>y());let{diffViewModel:_}=t;C();const w=_.keybindings;Ve(r,w,P=>e(11,d=P));const N=_.requestId;Ve(r,N,P=>e(9,a=P));let D,j="x",z="Copy request ID",W=()=>{};return r.$$set=P=>{"diffViewModel"in P&&C(e(0,_=P.diffViewModel))},r.$$.update=()=>{var P;2&r.$$.dirty&&(e(8,s=c.disableResolution),S(),S=he(s,Y=>e(12,u=Y))),2&r.$$.dirty&&(e(7,i=c.disableApply),y(),y=he(i,Y=>e(13,m=Y))),2&r.$$.dirty&&(c.currFocusedChunkIdx!==void 0?e(2,j=(c.currFocusedChunkIdx+1).toString()):e(2,j="x")),2&r.$$.dirty&&(e(6,n=c.isLoading),x(),x=he(n,Y=>e(10,h=Y))),2&r.$$.dirty&&e(5,o=!!((P=c.leaves)!=null&&P.length))},[_,c,j,z,W,o,n,i,s,a,h,d,u,m,w,N,function(P){P||(clearTimeout(D),D=void 0,e(3,z="Copy request ID"))},async function(){a&&(await navigator.clipboard.writeText(a),e(3,z="Copied!"),clearTimeout(D),D=setTimeout(W,1500))},function(P){W=P,e(4,W)}]}class ic extends ke{constructor(t){super(),Ae(this,t,sc,ec,Fe,{diffViewModel:0})}}function nc(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],i={};for(let n=0;n<s.length;n+=1)i=Ti(i,s[n]);return{c(){t=Io("svg"),e=new Eo(!0),this.h()},l(n){t=To(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=ko(t);e=Ao(o,!0),o.forEach(F),this.h()},h(){e.a=null,Fn(t,i)},m(n,o){Fo(n,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M133.9 232 65.8 95.9 383.4 232zm0 48h249.5L65.8 416.1l68-136.1zM44.6 34.6C32.3 29.3 17.9 32.3 8.7 42S-2.6 66.3 3.4 78.3L92.2 256 3.4 433.7c-6 12-3.9 26.5 5.3 36.3s23.5 12.7 35.9 7.5l448-192c11.8-5 19.4-16.6 19.4-29.4s-7.6-24.4-19.4-29.4l-448-192z"/>',t)},p(n,[o]){Fn(t,i=Oo(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&n[0]]))},i:Z,o:Z,d(n){n&&F(t)}}}function rc(r,t,e){return r.$$set=s=>{e(0,t=Ti(Ti({},t),On(s)))},[t=On(t)]}class oc extends ke{constructor(t){super(),Ae(this,t,rc,nc,Fe,{})}}var zr="Expected a function",Yn=NaN,ac="[object Symbol]",cc=/^\s+|\s+$/g,lc=/^[-+]0x[0-9a-f]+$/i,hc=/^0b[01]+$/i,uc=/^0o[0-7]+$/i,dc=parseInt,fc=typeof Me=="object"&&Me&&Me.Object===Object&&Me,mc=typeof self=="object"&&self&&self.Object===Object&&self,pc=fc||mc||Function("return this")(),gc=Object.prototype.toString,_c=Math.max,yc=Math.min,xi=function(){return pc.Date.now()};function vc(r,t,e){var s,i,n,o,a,c,h=0,d=!1,u=!1,m=!0;if(typeof r!="function")throw new TypeError(zr);function p(_){var w=s,N=i;return s=i=void 0,h=_,o=r.apply(N,w)}function C(_){var w=_-c;return c===void 0||w>=t||w<0||u&&_-h>=n}function x(){var _=xi();if(C(_))return S(_);a=setTimeout(x,function(w){var N=t-(w-c);return u?yc(N,n-(w-h)):N}(_))}function S(_){return a=void 0,m&&s?p(_):(s=i=void 0,o)}function y(){var _=xi(),w=C(_);if(s=arguments,i=this,c=_,w){if(a===void 0)return function(N){return h=N,a=setTimeout(x,t),d?p(N):o}(c);if(u)return a=setTimeout(x,t),p(c)}return a===void 0&&(a=setTimeout(x,t)),o}return t=tr(t)||0,ei(e)&&(d=!!e.leading,n=(u="maxWait"in e)?_c(tr(e.maxWait)||0,t):n,m="trailing"in e?!!e.trailing:m),y.cancel=function(){a!==void 0&&clearTimeout(a),h=0,s=c=i=a=void 0},y.flush=function(){return a===void 0?o:S(xi())},y}function ei(r){var t=typeof r;return!!r&&(t=="object"||t=="function")}function tr(r){if(typeof r=="number")return r;if(function(s){return typeof s=="symbol"||function(i){return!!i&&typeof i=="object"}(s)&&gc.call(s)==ac}(r))return Yn;if(ei(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=ei(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=r.replace(cc,"");var e=hc.test(r);return e||uc.test(r)?dc(r.slice(2),e?2:8):lc.test(r)?Yn:+r}const $c=$r(function(r,t,e){var s=!0,i=!0;if(typeof r!="function")throw new TypeError(zr);return ei(e)&&(s="leading"in e?!!e.leading:s,i="trailing"in e?!!e.trailing:i),vc(r,t,{leading:s,maxWait:t,trailing:i})});function fe(r){return Array.isArray?Array.isArray(r):Gr(r)==="[object Array]"}const Cc=1/0;function bc(r){return r==null?"":function(t){if(typeof t=="string")return t;let e=t+"";return e=="0"&&1/t==-Cc?"-0":e}(r)}function se(r){return typeof r=="string"}function jr(r){return typeof r=="number"}function Sc(r){return r===!0||r===!1||function(t){return Wr(t)&&t!==null}(r)&&Gr(r)=="[object Boolean]"}function Wr(r){return typeof r=="object"}function Ut(r){return r!=null}function wi(r){return!r.trim().length}function Gr(r){return r==null?r===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(r)}const xc=r=>`Missing ${r} property in key`,wc=r=>`Property 'weight' in key '${r}' must be a positive integer`,er=Object.prototype.hasOwnProperty;class Mc{constructor(t){this._keys=[],this._keyMap={};let e=0;t.forEach(s=>{let i=Vr(s);this._keys.push(i),this._keyMap[i.id]=i,e+=i.weight}),this._keys.forEach(s=>{s.weight/=e})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Vr(r){let t=null,e=null,s=null,i=1,n=null;if(se(r)||fe(r))s=r,t=sr(r),e=Gi(r);else{if(!er.call(r,"name"))throw new Error(xc("name"));const o=r.name;if(s=o,er.call(r,"weight")&&(i=r.weight,i<=0))throw new Error(wc(o));t=sr(o),e=Gi(o),n=r.getFn}return{path:t,id:e,weight:i,src:s,getFn:n}}function sr(r){return fe(r)?r:r.split(".")}function Gi(r){return fe(r)?r.join("."):r}var q={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(r,t)=>r.score===t.score?r.idx<t.idx?-1:1:r.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(r,t){let e=[],s=!1;const i=(n,o,a)=>{if(Ut(n))if(o[a]){const c=n[o[a]];if(!Ut(c))return;if(a===o.length-1&&(se(c)||jr(c)||Sc(c)))e.push(bc(c));else if(fe(c)){s=!0;for(let h=0,d=c.length;h<d;h+=1)i(c[h],o,a+1)}else o.length&&i(c,o,a+1)}else e.push(n)};return i(r,se(t)?t.split("."):t,0),s?e:e[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};const Ic=/[^ ]+/g;class sn{constructor({getFn:t=q.getFn,fieldNormWeight:e=q.fieldNormWeight}={}){this.norm=function(s=1,i=3){const n=new Map,o=Math.pow(10,i);return{get(a){const c=a.match(Ic).length;if(n.has(c))return n.get(c);const h=1/Math.pow(c,.5*s),d=parseFloat(Math.round(h*o)/o);return n.set(c,d),d},clear(){n.clear()}}}(e,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((e,s)=>{this._keysMap[e.id]=s})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,se(this.docs[0])?this.docs.forEach((t,e)=>{this._addString(t,e)}):this.docs.forEach((t,e)=>{this._addObject(t,e)}),this.norm.clear())}add(t){const e=this.size();se(t)?this._addString(t,e):this._addObject(t,e)}removeAt(t){this.records.splice(t,1);for(let e=t,s=this.size();e<s;e+=1)this.records[e].i-=1}getValueForItemAtKeyId(t,e){return t[this._keysMap[e]]}size(){return this.records.length}_addString(t,e){if(!Ut(t)||wi(t))return;let s={v:t,i:e,n:this.norm.get(t)};this.records.push(s)}_addObject(t,e){let s={i:e,$:{}};this.keys.forEach((i,n)=>{let o=i.getFn?i.getFn(t):this.getFn(t,i.path);if(Ut(o)){if(fe(o)){let a=[];const c=[{nestedArrIndex:-1,value:o}];for(;c.length;){const{nestedArrIndex:h,value:d}=c.pop();if(Ut(d))if(se(d)&&!wi(d)){let u={v:d,i:h,n:this.norm.get(d)};a.push(u)}else fe(d)&&d.forEach((u,m)=>{c.push({nestedArrIndex:m,value:u})})}s.$[n]=a}else if(se(o)&&!wi(o)){let a={v:o,n:this.norm.get(o)};s.$[n]=a}}}),this.records.push(s)}toJSON(){return{keys:this.keys,records:this.records}}}function Br(r,t,{getFn:e=q.getFn,fieldNormWeight:s=q.fieldNormWeight}={}){const i=new sn({getFn:e,fieldNormWeight:s});return i.setKeys(r.map(Vr)),i.setSources(t),i.create(),i}function js(r,{errors:t=0,currentLocation:e=0,expectedLocation:s=0,distance:i=q.distance,ignoreLocation:n=q.ignoreLocation}={}){const o=t/r.length;if(n)return o;const a=Math.abs(s-e);return i?o+a/i:a?1:o}const Pe=32;function Ec(r,t,e,{location:s=q.location,distance:i=q.distance,threshold:n=q.threshold,findAllMatches:o=q.findAllMatches,minMatchCharLength:a=q.minMatchCharLength,includeMatches:c=q.includeMatches,ignoreLocation:h=q.ignoreLocation}={}){if(t.length>Pe)throw new Error(`Pattern length exceeds max of ${Pe}.`);const d=t.length,u=r.length,m=Math.max(0,Math.min(s,u));let p=n,C=m;const x=a>1||c,S=x?Array(u):[];let y;for(;(y=r.indexOf(t,C))>-1;){let z=js(t,{currentLocation:y,expectedLocation:m,distance:i,ignoreLocation:h});if(p=Math.min(z,p),C=y+d,x){let W=0;for(;W<d;)S[y+W]=1,W+=1}}C=-1;let _=[],w=1,N=d+u;const D=1<<d-1;for(let z=0;z<d;z+=1){let W=0,P=N;for(;W<P;)js(t,{errors:z,currentLocation:m+P,expectedLocation:m,distance:i,ignoreLocation:h})<=p?W=P:N=P,P=Math.floor((N-W)/2+W);N=P;let Y=Math.max(1,m-P+1),ot=o?u:Math.min(m+P,u)+d,Tt=Array(ot+2);Tt[ot+1]=(1<<z)-1;for(let K=ot;K>=Y;K-=1){let G=K-1,Pt=e[r.charAt(G)];if(x&&(S[G]=+!!Pt),Tt[K]=(Tt[K+1]<<1|1)&Pt,z&&(Tt[K]|=(_[K+1]|_[K])<<1|1|_[K+1]),Tt[K]&D&&(w=js(t,{errors:z,currentLocation:G,expectedLocation:m,distance:i,ignoreLocation:h}),w<=p)){if(p=w,C=G,C<=m)break;Y=Math.max(1,2*m-C)}}if(js(t,{errors:z+1,currentLocation:m,expectedLocation:m,distance:i,ignoreLocation:h})>p)break;_=Tt}const j={isMatch:C>=0,score:Math.max(.001,w)};if(x){const z=function(W=[],P=q.minMatchCharLength){let Y=[],ot=-1,Tt=-1,K=0;for(let G=W.length;K<G;K+=1){let Pt=W[K];Pt&&ot===-1?ot=K:Pt||ot===-1||(Tt=K-1,Tt-ot+1>=P&&Y.push([ot,Tt]),ot=-1)}return W[K-1]&&K-ot>=P&&Y.push([ot,K-1]),Y}(S,a);z.length?c&&(j.indices=z):j.isMatch=!1}return j}function Tc(r){let t={};for(let e=0,s=r.length;e<s;e+=1){const i=r.charAt(e);t[i]=(t[i]||0)|1<<s-e-1}return t}class Kr{constructor(t,{location:e=q.location,threshold:s=q.threshold,distance:i=q.distance,includeMatches:n=q.includeMatches,findAllMatches:o=q.findAllMatches,minMatchCharLength:a=q.minMatchCharLength,isCaseSensitive:c=q.isCaseSensitive,ignoreLocation:h=q.ignoreLocation}={}){if(this.options={location:e,threshold:s,distance:i,includeMatches:n,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:c,ignoreLocation:h},this.pattern=c?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const d=(m,p)=>{this.chunks.push({pattern:m,alphabet:Tc(m),startIndex:p})},u=this.pattern.length;if(u>Pe){let m=0;const p=u%Pe,C=u-p;for(;m<C;)d(this.pattern.substr(m,Pe),m),m+=Pe;if(p){const x=u-Pe;d(this.pattern.substr(x),x)}}else d(this.pattern,0)}searchIn(t){const{isCaseSensitive:e,includeMatches:s}=this.options;if(e||(t=t.toLowerCase()),this.pattern===t){let C={isMatch:!0,score:0};return s&&(C.indices=[[0,t.length-1]]),C}const{location:i,distance:n,threshold:o,findAllMatches:a,minMatchCharLength:c,ignoreLocation:h}=this.options;let d=[],u=0,m=!1;this.chunks.forEach(({pattern:C,alphabet:x,startIndex:S})=>{const{isMatch:y,score:_,indices:w}=Ec(t,C,x,{location:i+S,distance:n,threshold:o,findAllMatches:a,minMatchCharLength:c,includeMatches:s,ignoreLocation:h});y&&(m=!0),u+=_,y&&w&&(d=[...d,...w])});let p={isMatch:m,score:m?u/this.chunks.length:1};return m&&s&&(p.indices=d),p}}class Se{constructor(t){this.pattern=t}static isMultiMatch(t){return ir(t,this.multiRegex)}static isSingleMatch(t){return ir(t,this.singleRegex)}search(){}}function ir(r,t){const e=r.match(t);return e?e[1]:null}class Qr extends Se{constructor(t,{location:e=q.location,threshold:s=q.threshold,distance:i=q.distance,includeMatches:n=q.includeMatches,findAllMatches:o=q.findAllMatches,minMatchCharLength:a=q.minMatchCharLength,isCaseSensitive:c=q.isCaseSensitive,ignoreLocation:h=q.ignoreLocation}={}){super(t),this._bitapSearch=new Kr(t,{location:e,threshold:s,distance:i,includeMatches:n,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:c,ignoreLocation:h})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class Xr extends Se{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let e,s=0;const i=[],n=this.pattern.length;for(;(e=t.indexOf(this.pattern,s))>-1;)s=e+n,i.push([e,s-1]);const o=!!i.length;return{isMatch:o,score:o?0:1,indices:i}}}const Vi=[class extends Se{constructor(r){super(r)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(r){const t=r===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},Xr,class extends Se{constructor(r){super(r)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(r){const t=r.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends Se{constructor(r){super(r)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(r){const t=!r.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},class extends Se{constructor(r){super(r)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(r){const t=!r.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},class extends Se{constructor(r){super(r)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(r){const t=r.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[r.length-this.pattern.length,r.length-1]}}},class extends Se{constructor(r){super(r)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(r){const t=r.indexOf(this.pattern)===-1;return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},Qr],nr=Vi.length,kc=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,Ac=new Set([Qr.type,Xr.type]);class Fc{constructor(t,{isCaseSensitive:e=q.isCaseSensitive,includeMatches:s=q.includeMatches,minMatchCharLength:i=q.minMatchCharLength,ignoreLocation:n=q.ignoreLocation,findAllMatches:o=q.findAllMatches,location:a=q.location,threshold:c=q.threshold,distance:h=q.distance}={}){this.query=null,this.options={isCaseSensitive:e,includeMatches:s,minMatchCharLength:i,findAllMatches:o,ignoreLocation:n,location:a,threshold:c,distance:h},this.pattern=e?t:t.toLowerCase(),this.query=function(d,u={}){return d.split("|").map(m=>{let p=m.trim().split(kc).filter(x=>x&&!!x.trim()),C=[];for(let x=0,S=p.length;x<S;x+=1){const y=p[x];let _=!1,w=-1;for(;!_&&++w<nr;){const N=Vi[w];let D=N.isMultiMatch(y);D&&(C.push(new N(D,u)),_=!0)}if(!_)for(w=-1;++w<nr;){const N=Vi[w];let D=N.isSingleMatch(y);if(D){C.push(new N(D,u));break}}}return C})}(this.pattern,this.options)}static condition(t,e){return e.useExtendedSearch}searchIn(t){const e=this.query;if(!e)return{isMatch:!1,score:1};const{includeMatches:s,isCaseSensitive:i}=this.options;t=i?t:t.toLowerCase();let n=0,o=[],a=0;for(let c=0,h=e.length;c<h;c+=1){const d=e[c];o.length=0,n=0;for(let u=0,m=d.length;u<m;u+=1){const p=d[u],{isMatch:C,indices:x,score:S}=p.search(t);if(!C){a=0,n=0,o.length=0;break}if(n+=1,a+=S,s){const y=p.constructor.type;Ac.has(y)?o=[...o,...x]:o.push(x)}}if(n){let u={isMatch:!0,score:a/n};return s&&(u.indices=o),u}}return{isMatch:!1,score:1}}}const Bi=[];function Ki(r,t){for(let e=0,s=Bi.length;e<s;e+=1){let i=Bi[e];if(i.condition(r,t))return new i(r,t)}return new Kr(r,t)}const nn="$and",Oc="$or",rr="$path",Nc="$val",Mi=r=>!(!r[nn]&&!r[Oc]),or=r=>({[nn]:Object.keys(r).map(t=>({[t]:r[t]}))});function Zr(r,t,{auto:e=!0}={}){const s=i=>{let n=Object.keys(i);const o=(c=>!!c[rr])(i);if(!o&&n.length>1&&!Mi(i))return s(or(i));if((c=>!fe(c)&&Wr(c)&&!Mi(c))(i)){const c=o?i[rr]:n[0],h=o?i[Nc]:i[c];if(!se(h))throw new Error((u=>`Invalid value for key ${u}`)(c));const d={keyId:Gi(c),pattern:h};return e&&(d.searcher=Ki(h,t)),d}let a={children:[],operator:n[0]};return n.forEach(c=>{const h=i[c];fe(h)&&h.forEach(d=>{a.children.push(s(d))})}),a};return Mi(r)||(r=or(r)),s(r)}function Lc(r,t){const e=r.matches;t.matches=[],Ut(e)&&e.forEach(s=>{if(!Ut(s.indices)||!s.indices.length)return;const{indices:i,value:n}=s;let o={indices:i,value:n};s.key&&(o.key=s.key.src),s.idx>-1&&(o.refIndex=s.idx),t.matches.push(o)})}function Rc(r,t){t.score=r.score}class Ye{constructor(t,e={},s){this.options={...q,...e},this.options.useExtendedSearch,this._keyStore=new Mc(this.options.keys),this.setCollection(t,s)}setCollection(t,e){if(this._docs=t,e&&!(e instanceof sn))throw new Error("Incorrect 'index' type");this._myIndex=e||Br(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){Ut(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const e=[];for(let s=0,i=this._docs.length;s<i;s+=1){const n=this._docs[s];t(n,s)&&(this.removeAt(s),s-=1,i-=1,e.push(n))}return e}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:e=-1}={}){const{includeMatches:s,includeScore:i,shouldSort:n,sortFn:o,ignoreFieldNorm:a}=this.options;let c=se(t)?se(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(h,{ignoreFieldNorm:d=q.ignoreFieldNorm}){h.forEach(u=>{let m=1;u.matches.forEach(({key:p,norm:C,score:x})=>{const S=p?p.weight:null;m*=Math.pow(x===0&&S?Number.EPSILON:x,(S||1)*(d?1:C))}),u.score=m})}(c,{ignoreFieldNorm:a}),n&&c.sort(o),jr(e)&&e>-1&&(c=c.slice(0,e)),function(h,d,{includeMatches:u=q.includeMatches,includeScore:m=q.includeScore}={}){const p=[];return u&&p.push(Lc),m&&p.push(Rc),h.map(C=>{const{idx:x}=C,S={item:d[x],refIndex:x};return p.length&&p.forEach(y=>{y(C,S)}),S})}(c,this._docs,{includeMatches:s,includeScore:i})}_searchStringList(t){const e=Ki(t,this.options),{records:s}=this._myIndex,i=[];return s.forEach(({v:n,i:o,n:a})=>{if(!Ut(n))return;const{isMatch:c,score:h,indices:d}=e.searchIn(n);c&&i.push({item:n,idx:o,matches:[{score:h,value:n,norm:a,indices:d}]})}),i}_searchLogical(t){const e=Zr(t,this.options),s=(a,c,h)=>{if(!a.children){const{keyId:u,searcher:m}=a,p=this._findMatches({key:this._keyStore.get(u),value:this._myIndex.getValueForItemAtKeyId(c,u),searcher:m});return p&&p.length?[{idx:h,item:c,matches:p}]:[]}const d=[];for(let u=0,m=a.children.length;u<m;u+=1){const p=a.children[u],C=s(p,c,h);if(C.length)d.push(...C);else if(a.operator===nn)return[]}return d},i=this._myIndex.records,n={},o=[];return i.forEach(({$:a,i:c})=>{if(Ut(a)){let h=s(e,a,c);h.length&&(n[c]||(n[c]={idx:c,item:a,matches:[]},o.push(n[c])),h.forEach(({matches:d})=>{n[c].matches.push(...d)}))}}),o}_searchObjectList(t){const e=Ki(t,this.options),{keys:s,records:i}=this._myIndex,n=[];return i.forEach(({$:o,i:a})=>{if(!Ut(o))return;let c=[];s.forEach((h,d)=>{c.push(...this._findMatches({key:h,value:o[d],searcher:e}))}),c.length&&n.push({idx:a,item:o,matches:c})}),n}_findMatches({key:t,value:e,searcher:s}){if(!Ut(e))return[];let i=[];if(fe(e))e.forEach(({v:n,i:o,n:a})=>{if(!Ut(n))return;const{isMatch:c,score:h,indices:d}=s.searchIn(n);c&&i.push({score:h,key:t,value:n,idx:o,norm:a,indices:d})});else{const{v:n,n:o}=e,{isMatch:a,score:c,indices:h}=s.searchIn(n);a&&i.push({score:c,key:t,value:n,norm:o,indices:h})}return i}}Ye.version="7.0.0",Ye.createIndex=Br,Ye.parseIndex=function(r,{getFn:t=q.getFn,fieldNormWeight:e=q.fieldNormWeight}={}){const{keys:s,records:i}=r,n=new sn({getFn:t,fieldNormWeight:e});return n.setKeys(s),n.setIndexRecords(i),n},Ye.config=q,Ye.parseQuery=Zr,function(...r){Bi.push(...r)}(Fc);const xe=class xe{constructor(t,e){l(this,"_disposers",[]);l(this,"_allMentionables",ut([]));l(this,"_breadcrumbIds",ut([]));l(this,"_userQuery",ut(""));l(this,"_active",ut(!1));l(this,"_allGroups",pi([this._active,this._allMentionables],([t,e])=>t?Xo(e):[]));l(this,"_currentGroup",pi([this._breadcrumbIds,this._allGroups],([t,e])=>{if(t.length===0)return;const s=t[t.length-1];return e.find(i=>Cs(i)&&i.id===s)}));l(this,"dispose",()=>{for(const t of this._disposers)t()});l(this,"openDropdown",()=>{this._active.set(!0)});l(this,"closeDropdown",()=>{this._active.set(!1),this._resetState()});l(this,"toggleDropdown",()=>It(this._active)?(this.closeDropdown(),!1):(this.openDropdown(),!0));l(this,"pushBreadcrumb",t=>{It(this._active)&&this._breadcrumbIds.update(e=>[...e,t.id])});l(this,"popBreadcrumb",()=>{It(this._active)&&this._breadcrumbIds.update(t=>t.slice(0,-1))});l(this,"selectMentionable",t=>{var i;const e=this._chatModel.extensionClient,s=this._chatModel.specialContextInputModel;return Cs(t)&&t.type==="breadcrumb"?(this.pushBreadcrumb(t),!0):t.type==="breadcrumb-back"?(this.popBreadcrumb(),!0):Mr(t)?(s.markAllActive(),this.closeDropdown(),e.reportWebviewClientEvent(Oi.chatRestoreDefaultContext),!0):t.clearContext?(s.markAllInactive(),this.closeDropdown(),e.reportWebviewClientEvent(Oi.chatClearContext),!0):t.userGuidelines?(e.openSettingsPage("guidelines"),this.closeDropdown(),!0):((i=this._insertMentionNode)==null||i.call(this,t),this.closeDropdown(),!0)});l(this,"_displayItems",pi([this._active,this._breadcrumbIds,this._userQuery,this._currentGroup,this.allGroups],([t,e,s,i,n])=>{if(!t)return[];if(e.length>0&&i)return[{...i,type:"breadcrumb-back"},...i.group.items.slice(0,xe.SINGLE_GROUP_MAX_ITEMS).map(o=>({...o,type:"item"}))];if(s.length>0){const o=Dc(It(this._userQuery)).map(a=>({...a,type:"item"}));return n.flatMap(a=>[{...a,type:"breadcrumb"},...a.group.items.slice(0,xe.MULTI_GROUP_MAX_ITEMS).map(c=>({...c,type:"item"}))]).concat(o)}return[{...Ir,type:"item"},...n.map(o=>({...o,type:"breadcrumb"})),{...Er,type:"item"},{...Tr,type:"item"}]}));l(this,"_refreshSeqNum",0);l(this,"_refreshMentionables",$c(async()=>{if(!It(this._active))return;this._refreshSeqNum++;const t=this._refreshSeqNum,e=this._chatModel.currentConversationModel&&He(this._chatModel.currentConversationModel),s=It(this._userQuery),i=await this._chatModel.extensionClient.getSuggestions(s,e);t===this._refreshSeqNum&&this._allMentionables.set(Jr({query:s,mentionables:i}))},xe.REFRESH_THROTTLE_MS,{leading:!0,trailing:!0}));this._chatModel=t,this._insertMentionNode=e,this._disposers.push(this._userQuery.subscribe(this._refreshMentionables)),this._disposers.push(this._active.subscribe(this._refreshMentionables))}get allGroups(){return this._allGroups}get currentGroup(){return this._currentGroup}get breadcrumbIds(){return this._breadcrumbIds}get displayItems(){return this._displayItems}get active(){return this._active}get userQuery(){return this._userQuery}_resetState(){this._breadcrumbIds.set([]),this._userQuery.set("")}};l(xe,"REFRESH_THROTTLE_MS",600),l(xe,"SINGLE_GROUP_MAX_ITEMS",12),l(xe,"MULTI_GROUP_MAX_ITEMS",6);let Qi=xe;const Jr=({query:r,mentionables:t,returnAllIfNoResults:e=!0,threshold:s=1})=>{if(r.length<=1)return t;const i=new Ye(t,{keys:["label"],threshold:s,minMatchCharLength:0,ignoreLocation:!0,includeScore:!0,useExtendedSearch:!1,shouldSort:!0,findAllMatches:!0}).search(r);return i.length===0&&e?t:i.map(n=>n.item)},Dc=r=>Jr({query:r,mentionables:[Ir,Er,Tr],returnAllIfNoResults:!1,threshold:.6});function si(r){switch(r){case le.DEFAULT:return Un;case le.PROTOTYPER:return da;case le.BRAINSTORM:return ua;case le.REVIEWER:return ha;default:return Un}}function qc(r){let t,e,s,i=r[0].label+"";return{c(){t=et("span"),e=et("span"),s=ct(i),X(e,"class","c-mentionable-group-label__text right"),X(t,"class","c-mentionable-group-label")},m(n,o){O(n,t,o),st(t,e),st(e,s)},p(n,o){1&o&&i!==(i=n[0].label+"")&&Ht(s,i)},i:Z,o:Z,d(n){n&&F(t)}}}function Uc(r){let t,e;return t=new fa({props:{$$slots:{text:[Qc],leftIcon:[Kc]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};17&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Hc(r){let t,e=r[0].label+"";return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){1&i&&e!==(e=s[0].label+"")&&Ht(t,e)},i:Z,o:Z,d(s){s&&F(t)}}}function Pc(r){let t,e;return t=new Ot({props:{filepath:r[0].rule.path,$$slots:{leftIcon:[Xc]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};1&i&&(n.filepath=s[0].rule.path),16&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function zc(r){let t,e;return t=new Ot({props:{filepath:r[0].recentFile.pathName,$$slots:{leftIcon:[Zc]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};1&i&&(n.filepath=s[0].recentFile.pathName),16&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function jc(r){let t,e;return t=new Ot({props:{filepath:r[0].selection.pathName,$$slots:{leftIcon:[Jc]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};1&i&&(n.filepath=s[0].selection.pathName),16&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Wc(r){let t,e;return t=new Ot({props:{filepath:r[0].sourceFolder.folderRoot,$$slots:{leftIcon:[Yc]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};1&i&&(n.filepath=s[0].sourceFolder.folderRoot),16&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Gc(r){let t,e;return t=new Ot({props:{filepath:r[0].externalSource.name,$$slots:{leftIcon:[tl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};1&i&&(n.filepath=s[0].externalSource.name),16&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Vc(r){let t,e;return t=new Ot({props:{filepath:r[0].folder.pathName,$$slots:{leftIcon:[el]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};1&i&&(n.filepath=s[0].folder.pathName),16&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Bc(r){let t,e;return t=new Ot({props:{filepath:r[0].file.pathName,$$slots:{leftIcon:[sl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};1&i&&(n.filepath=s[0].file.pathName),16&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Kc(r){let t,e,s;var i=si(r[0].personality.type);return i&&(e=cs(i,{})),{c(){t=et("span"),e&&E(e.$$.fragment),X(t,"slot","leftIcon"),X(t,"class","c-context-menu-item__icon svelte-1a2w9oo")},m(n,o){O(n,t,o),e&&T(e,t,null),s=!0},p(n,o){if(1&o&&i!==(i=si(n[0].personality.type))){if(e){gt();const a=e;b(a.$$.fragment,1,0,()=>{k(a,1)}),_t()}i?(e=cs(i,{}),E(e.$$.fragment),$(e.$$.fragment,1),T(e,t,null)):e=null}},i(n){s||(e&&$(e.$$.fragment,n),s=!0)},o(n){e&&b(e.$$.fragment,n),s=!1},d(n){n&&F(t),e&&k(e)}}}function Qc(r){let t,e,s=r[0].label+"";return{c(){t=et("span"),e=ct(s),X(t,"slot","text")},m(i,n){O(i,t,n),st(t,e)},p(i,n){1&n&&s!==(s=i[0].label+"")&&Ht(e,s)},d(i){i&&F(t)}}}function Xc(r){let t,e;return t=new Nr({props:{slot:"leftIcon",iconName:"rule"}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Zc(r){let t,e;return t=new hs({props:{slot:"leftIcon",iconName:"description"}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Jc(r){let t,e;return t=new hs({props:{slot:"leftIcon",iconName:"text_select_start"}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Yc(r){let t,e;return t=new hs({props:{slot:"leftIcon",iconName:"folder_managed"}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function tl(r){let t,e;return t=new hs({props:{slot:"leftIcon",iconName:"import_contacts"}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function el(r){let t,e;return t=new hs({props:{slot:"leftIcon",iconName:"folder_open"}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function sl(r){let t,e;return t=new hs({props:{slot:"leftIcon",iconName:"description"}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function il(r){let t,e,s,i,n,o,a,c,h,d,u,m,p,C;const x=[Bc,Vc,Gc,Wc,jc,zc,Pc,Hc,Uc,qc],S=[];function y(_,w){return 1&w&&(t=null),1&w&&(e=null),1&w&&(s=null),1&w&&(i=null),1&w&&(n=null),1&w&&(o=null),1&w&&(a=null),1&w&&(c=null),1&w&&(h=null),1&w&&(d=null),t==null&&(t=!!Ji(_[0])),t?0:(e==null&&(e=!!Yi(_[0])),e?1:(s==null&&(s=!!tn(_[0])),s?2:(i==null&&(i=!!Zs(_[0])),i?3:(n==null&&(n=!!Xs(_[0])),n?4:(o==null&&(o=!!Qs(_[0])),o?5:(a==null&&(a=!!Ys(_[0])),a?6:(c==null&&(c=!!Cs(_[0])),c?7:(h==null&&(h=!!ii(_[0])),h?8:(d==null&&(d=!!(Mr(_[0])||Zo(_[0])||Js(_[0]))),d?9:-1)))))))))}return~(u=y(r,-1))&&(m=S[u]=x[u](r)),{c(){m&&m.c(),p=ie()},m(_,w){~u&&S[u].m(_,w),O(_,p,w),C=!0},p(_,w){let N=u;u=y(_,w),u===N?~u&&S[u].p(_,w):(m&&(gt(),b(S[N],1,1,()=>{S[N]=null}),_t()),~u?(m=S[u],m?m.p(_,w):(m=S[u]=x[u](_),m.c()),$(m,1),m.m(p.parentNode,p)):m=null)},i(_){C||($(m),C=!0)},o(_){b(m),C=!1},d(_){_&&F(p),~u&&S[u].d(_)}}}function nl(r){let t,e,s;var i=r[3];function n(o,a){return{props:{highlight:o[2],onSelect:o[1],$$slots:{default:[il]},$$scope:{ctx:o}}}}return i&&(t=cs(i,n(r))),{c(){t&&E(t.$$.fragment),e=ie()},m(o,a){t&&T(t,o,a),O(o,e,a),s=!0},p(o,[a]){if(8&a&&i!==(i=o[3])){if(t){gt();const c=t;b(c.$$.fragment,1,0,()=>{k(c,1)}),_t()}i?(t=cs(i,n(o)),E(t.$$.fragment),$(t.$$.fragment,1),T(t,e.parentNode,e)):t=null}else if(i){const c={};4&a&&(c.highlight=o[2]),2&a&&(c.onSelect=o[1]),17&a&&(c.$$scope={dirty:a,ctx:o}),t.$set(c)}},i(o){s||(t&&$(t.$$.fragment,o),s=!0)},o(o){t&&b(t.$$.fragment,o),s=!1},d(o){o&&F(e),t&&k(t,o)}}}function rl(r,t,e){let s,{item:i}=t,{onSelect:n}=t,{highlight:o}=t;return r.$$set=a=>{"item"in a&&e(0,i=a.item),"onSelect"in a&&e(1,n=a.onSelect),"highlight"in a&&e(2,o=a.highlight)},r.$$.update=()=>{1&r.$$.dirty&&(i.type==="breadcrumb-back"?e(3,s=Ci.BreadcrumbBackItem):i.type==="breadcrumb"&&Cs(i)?e(3,s=Ci.BreadcrumbItem):i.type!=="item"||Cs(i)||e(3,s=Ci.Item))},[i,n,o,s]}class ol extends ke{constructor(t){super(),Ae(this,t,rl,nl,Fe,{item:0,onSelect:1,highlight:2})}}function al(r){let t,e=r[0].label+"";return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){1&i&&e!==(e=s[0].label+"")&&Ht(t,e)},i:Z,o:Z,d(s){s&&F(t)}}}function cl(r){let t,e,s,i;return t=new Nr({}),s=new Ot({props:{filepath:`${Dn}/${qn}/${r[0].rule.path}`}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(n,o){T(t,n,o),O(n,e,o),T(s,n,o),i=!0},p(n,o){const a={};1&o&&(a.filepath=`${Dn}/${qn}/${n[0].rule.path}`),s.$set(a)},i(n){i||($(t.$$.fragment,n),$(s.$$.fragment,n),i=!0)},o(n){b(t.$$.fragment,n),b(s.$$.fragment,n),i=!1},d(n){n&&F(e),k(t,n),k(s,n)}}}function ll(r){let t,e,s,i,n=r[0].task.taskTree.description&&r[0].task.taskTree.description.trim();e=new ls({props:{size:2,weight:"bold",$$slots:{default:[yl]},$$scope:{ctx:r}}});let o=n&&ar(r);return{c(){t=et("div"),E(e.$$.fragment),s=J(),o&&o.c(),X(t,"class","c-mention-hover-contents__task svelte-p7en3g")},m(a,c){O(a,t,c),T(e,t,null),st(t,s),o&&o.m(t,null),i=!0},p(a,c){const h={};3&c&&(h.$$scope={dirty:c,ctx:a}),e.$set(h),1&c&&(n=a[0].task.taskTree.description&&a[0].task.taskTree.description.trim()),n?o?(o.p(a,c),1&c&&$(o,1)):(o=ar(a),o.c(),$(o,1),o.m(t,null)):o&&(gt(),b(o,1,1,()=>{o=null}),_t())},i(a){i||($(e.$$.fragment,a),$(o),i=!0)},o(a){b(e.$$.fragment,a),b(o),i=!1},d(a){a&&F(t),k(e),o&&o.d()}}}function hl(r){let t,e,s,i,n,o,a,c;return e=new ma({props:{heightPx:32,floatHeight:4,animationDuration:2.25,$$slots:{default:[$l]},$$scope:{ctx:r}}}),n=new ls({props:{size:2,weight:"medium",$$slots:{default:[Cl]},$$scope:{ctx:r}}}),a=new ls({props:{size:1,$$slots:{default:[bl]},$$scope:{ctx:r}}}),{c(){t=et("div"),E(e.$$.fragment),s=J(),i=et("div"),E(n.$$.fragment),o=J(),E(a.$$.fragment),X(t,"class","c-mention-hover-contents__personality-icon svelte-p7en3g"),X(i,"class","c-mention-hover-contents__personality svelte-p7en3g")},m(h,d){O(h,t,d),T(e,t,null),O(h,s,d),O(h,i,d),T(n,i,null),st(i,o),T(a,i,null),c=!0},p(h,d){const u={};3&d&&(u.$$scope={dirty:d,ctx:h}),e.$set(u);const m={};3&d&&(m.$$scope={dirty:d,ctx:h}),n.$set(m);const p={};3&d&&(p.$$scope={dirty:d,ctx:h}),a.$set(p)},i(h){c||($(e.$$.fragment,h),$(n.$$.fragment,h),$(a.$$.fragment,h),c=!0)},o(h){b(e.$$.fragment,h),b(n.$$.fragment,h),b(a.$$.fragment,h),c=!1},d(h){h&&(F(t),F(s),F(i)),k(e),k(n),k(a)}}}function ul(r){var n,o;let t,e,s,i;return t=new pa({}),s=new Ot({props:{filepath:`${r[0].selection.pathName}:L${(n=r[0].selection.fullRange)==null?void 0:n.startLineNumber}-${(o=r[0].selection.fullRange)==null?void 0:o.endLineNumber}`}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(a,c){T(t,a,c),O(a,e,c),T(s,a,c),i=!0},p(a,c){var d,u;const h={};1&c&&(h.filepath=`${a[0].selection.pathName}:L${(d=a[0].selection.fullRange)==null?void 0:d.startLineNumber}-${(u=a[0].selection.fullRange)==null?void 0:u.endLineNumber}`),s.$set(h)},i(a){i||($(t.$$.fragment,a),$(s.$$.fragment,a),i=!0)},o(a){b(t.$$.fragment,a),b(s.$$.fragment,a),i=!1},d(a){a&&F(e),k(t,a),k(s,a)}}}function dl(r){var i;let t,e,s=(r[0].userGuidelines.overLimit||((i=r[0].rulesAndGuidelinesState)==null?void 0:i.overLimit))&&cr(r);return{c(){s&&s.c(),t=ie()},m(n,o){s&&s.m(n,o),O(n,t,o),e=!0},p(n,o){var a;n[0].userGuidelines.overLimit||(a=n[0].rulesAndGuidelinesState)!=null&&a.overLimit?s?(s.p(n,o),1&o&&$(s,1)):(s=cr(n),s.c(),$(s,1),s.m(t.parentNode,t)):s&&(gt(),b(s,1,1,()=>{s=null}),_t())},i(n){e||($(s),e=!0)},o(n){b(s),e=!1},d(n){n&&F(t),s&&s.d(n)}}}function fl(r){let t,e,s,i,n,o,a,c;return s=new ga({}),n=new Ot({props:{class:"c-source-folder-item",filepath:r[0].sourceFolder.folderRoot}}),a=new _a({props:{class:"guidelines-filespan",sourceFolder:r[0].sourceFolder}}),{c(){t=et("div"),e=et("div"),E(s.$$.fragment),i=J(),E(n.$$.fragment),o=J(),E(a.$$.fragment),X(e,"class","l-source-folder-name svelte-p7en3g"),X(t,"class","l-mention-hover-contents__source-folder")},m(h,d){O(h,t,d),st(t,e),T(s,e,null),st(e,i),T(n,e,null),st(t,o),T(a,t,null),c=!0},p(h,d){const u={};1&d&&(u.filepath=h[0].sourceFolder.folderRoot),n.$set(u);const m={};1&d&&(m.sourceFolder=h[0].sourceFolder),a.$set(m)},i(h){c||($(s.$$.fragment,h),$(n.$$.fragment,h),$(a.$$.fragment,h),c=!0)},o(h){b(s.$$.fragment,h),b(n.$$.fragment,h),b(a.$$.fragment,h),c=!1},d(h){h&&F(t),k(s),k(n),k(a)}}}function ml(r){let t,e,s,i;return t=new ya({}),s=new Ot({props:{filepath:r[0].externalSource.name}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(n,o){T(t,n,o),O(n,e,o),T(s,n,o),i=!0},p(n,o){const a={};1&o&&(a.filepath=n[0].externalSource.name),s.$set(a)},i(n){i||($(t.$$.fragment,n),$(s.$$.fragment,n),i=!0)},o(n){b(t.$$.fragment,n),b(s.$$.fragment,n),i=!1},d(n){n&&F(e),k(t,n),k(s,n)}}}function pl(r){let t,e,s,i;return t=new aa({}),s=new Ot({props:{filepath:r[0].folder.pathName}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(n,o){T(t,n,o),O(n,e,o),T(s,n,o),i=!0},p(n,o){const a={};1&o&&(a.filepath=n[0].folder.pathName),s.$set(a)},i(n){i||($(t.$$.fragment,n),$(s.$$.fragment,n),i=!0)},o(n){b(t.$$.fragment,n),b(s.$$.fragment,n),i=!1},d(n){n&&F(e),k(t,n),k(s,n)}}}function gl(r){let t,e,s,i;return t=new Or({}),s=new Ot({props:{filepath:r[0].recentFile.pathName}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(n,o){T(t,n,o),O(n,e,o),T(s,n,o),i=!0},p(n,o){const a={};1&o&&(a.filepath=n[0].recentFile.pathName),s.$set(a)},i(n){i||($(t.$$.fragment,n),$(s.$$.fragment,n),i=!0)},o(n){b(t.$$.fragment,n),b(s.$$.fragment,n),i=!1},d(n){n&&F(e),k(t,n),k(s,n)}}}function _l(r){let t,e,s,i;return t=new Or({}),s=new Ot({props:{filepath:r[0].file.pathName}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(n,o){T(t,n,o),O(n,e,o),T(s,n,o),i=!0},p(n,o){const a={};1&o&&(a.filepath=n[0].file.pathName),s.$set(a)},i(n){i||($(t.$$.fragment,n),$(s.$$.fragment,n),i=!0)},o(n){b(t.$$.fragment,n),b(s.$$.fragment,n),i=!1},d(n){n&&F(e),k(t,n),k(s,n)}}}function yl(r){let t,e=r[0].task.taskTree.name+"";return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){1&i&&e!==(e=s[0].task.taskTree.name+"")&&Ht(t,e)},d(s){s&&F(t)}}}function ar(r){let t,e;return t=new ls({props:{size:1,$$slots:{default:[vl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};3&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function vl(r){let t,e=r[0].task.taskTree.description.trim().replace(/\s+/g," ")+"";return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){1&i&&e!==(e=s[0].task.taskTree.description.trim().replace(/\s+/g," ")+"")&&Ht(t,e)},d(s){s&&F(t)}}}function $l(r){let t,e,s;var i=si(r[0].personality.type);return i&&(t=cs(i,{})),{c(){t&&E(t.$$.fragment),e=ie()},m(n,o){t&&T(t,n,o),O(n,e,o),s=!0},p(n,o){if(1&o&&i!==(i=si(n[0].personality.type))){if(t){gt();const a=t;b(a.$$.fragment,1,0,()=>{k(a,1)}),_t()}i?(t=cs(i,{}),E(t.$$.fragment),$(t.$$.fragment,1),T(t,e.parentNode,e)):t=null}},i(n){s||(t&&$(t.$$.fragment,n),s=!0)},o(n){t&&b(t.$$.fragment,n),s=!1},d(n){n&&F(e),t&&k(t,n)}}}function Cl(r){let t,e=r[0].label+"";return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){1&i&&e!==(e=s[0].label+"")&&Ht(t,e)},d(s){s&&F(t)}}}function bl(r){let t,e=r[0].personality.description+"";return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){1&i&&e!==(e=s[0].personality.description+"")&&Ht(t,e)},d(s){s&&F(t)}}}function cr(r){let t,e,s,i;const n=[xl,Sl],o=[];function a(c,h){var d;return(d=c[0].rulesAndGuidelinesState)!=null&&d.overLimit?0:c[0].userGuidelines.overLimit?1:-1}return~(t=a(r))&&(e=o[t]=n[t](r)),{c(){e&&e.c(),s=ie()},m(c,h){~t&&o[t].m(c,h),O(c,s,h),i=!0},p(c,h){let d=t;t=a(c),t===d?~t&&o[t].p(c,h):(e&&(gt(),b(o[d],1,1,()=>{o[d]=null}),_t()),~t?(e=o[t],e?e.p(c,h):(e=o[t]=n[t](c),e.c()),$(e,1),e.m(s.parentNode,s)):e=null)},i(c){i||($(e),i=!0)},o(c){b(e),i=!1},d(c){c&&F(s),~t&&o[t].d(c)}}}function Sl(r){let t,e;return t=new ls({props:{size:1,$$slots:{default:[wl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};3&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function xl(r){let t,e;return t=new ls({props:{size:1,$$slots:{default:[Ml]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};3&i&&(n.$$scope={dirty:i,ctx:s}),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function wl(r){let t,e=`Guidelines exceeded length limit of ${r[0].userGuidelines.lengthLimit} characters`;return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){1&i&&e!==(e=`Guidelines exceeded length limit of ${s[0].userGuidelines.lengthLimit} characters`)&&Ht(t,e)},d(s){s&&F(t)}}}function Ml(r){let t,e=`Rules and workspace guidelines (${r[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${r[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`;return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){1&i&&e!==(e=`Rules and workspace guidelines (${s[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${s[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`)&&Ht(t,e)},d(s){s&&F(t)}}}function Il(r){let t,e,s,i,n,o,a,c,h,d,u,m,p,C;const x=[_l,gl,pl,ml,fl,dl,ul,hl,ll,cl,al],S=[];function y(_,w){return 1&w&&(e=null),1&w&&(s=null),1&w&&(i=null),1&w&&(n=null),1&w&&(o=null),1&w&&(a=null),1&w&&(c=null),1&w&&(h=null),1&w&&(d=null),1&w&&(u=null),e==null&&(e=!(!_[0]||!Ji(_[0]))),e?0:(s==null&&(s=!(!_[0]||!Qs(_[0]))),s?1:(i==null&&(i=!(!_[0]||!Yi(_[0]))),i?2:(n==null&&(n=!(!_[0]||!tn(_[0]))),n?3:(o==null&&(o=!(!_[0]||!Zs(_[0]))),o?4:(a==null&&(a=!(!_[0]||!Js(_[0]))),a?5:(c==null&&(c=!(!_[0]||!Xs(_[0]))),c?6:(h==null&&(h=!(!_[0]||!ii(_[0]))),h?7:(d==null&&(d=!(!_[0]||!wr(_[0]))),d?8:(u==null&&(u=!(!_[0]||!Ys(_[0]))),u?9:10)))))))))}return m=y(r,-1),p=S[m]=x[m](r),{c(){t=et("div"),p.c(),X(t,"class","c-mention-hover-contents svelte-p7en3g")},m(_,w){O(_,t,w),S[m].m(t,null),C=!0},p(_,[w]){let N=m;m=y(_,w),m===N?S[m].p(_,w):(gt(),b(S[N],1,1,()=>{S[N]=null}),_t(),p=S[m],p?p.p(_,w):(p=S[m]=x[m](_),p.c()),$(p,1),p.m(t,null))},i(_){C||($(p),C=!0)},o(_){b(p),C=!1},d(_){_&&F(t),S[m].d()}}}function El(r,t,e){let{option:s}=t;return r.$$set=i=>{"option"in i&&e(0,s=i.option)},[s]}class Tl extends ke{constructor(t){super(),Ae(this,t,El,Il,Fe,{option:0})}}function lr(r,t,e){const s=r.slice();return s[15]=t[e],s}function hr(r){let t,e;function s(){return r[8](r[15])}return t=new ol({props:{item:r[15],highlight:r[15]===r[14],onSelect:s}}),{c(){E(t.$$.fragment)},m(i,n){T(t,i,n),e=!0},p(i,n){r=i;const o={};4&n&&(o.item=r[15]),16388&n&&(o.highlight=r[15]===r[14]),4&n&&(o.onSelect=s),t.$set(o)},i(i){e||($(t.$$.fragment,i),e=!0)},o(i){b(t.$$.fragment,i),e=!1},d(i){k(t,i)}}}function kl(r){let t,e,s=Ks(r[2]),i=[];for(let o=0;o<s.length;o+=1)i[o]=hr(lr(r,s,o));const n=o=>b(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();t=ie()},m(o,a){for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(o,a);O(o,t,a),e=!0},p(o,a){if(16420&a){let c;for(s=Ks(o[2]),c=0;c<s.length;c+=1){const h=lr(o,s,c);i[c]?(i[c].p(h,a),$(i[c],1)):(i[c]=hr(h),i[c].c(),$(i[c],1),i[c].m(t.parentNode,t))}for(gt(),c=s.length;c<i.length;c+=1)n(c);_t()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)$(i[a]);e=!0}},o(o){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)b(i[a]);e=!1},d(o){o&&F(t),Sr(i,o)}}}function Al(r){let t,e;return t=new Tl({props:{slot:"mentionable",option:r[13]}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p(s,i){const n={};8192&i&&(n.option=s[13]),t.$set(n)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Fl(r){let t,e,s,i;return t=new Ni.Menu.Root({props:{mentionables:r[2],onQueryUpdate:r[4],onSelectMentionable:r[5],$$slots:{default:[kl,({activeItem:n})=>({14:n}),({activeItem:n})=>n?16384:0]},$$scope:{ctx:r}}}),s=new Ni.ChipTooltip({props:{$$slots:{mentionable:[Al,({mentionable:n})=>({13:n}),({mentionable:n})=>n?8192:0]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(n,o){T(t,n,o),O(n,e,o),T(s,n,o),i=!0},p(n,o){const a={};4&o&&(a.mentionables=n[2]),278532&o&&(a.$$scope={dirty:o,ctx:n}),t.$set(a);const c={};270336&o&&(c.$$scope={dirty:o,ctx:n}),s.$set(c)},i(n){i||($(t.$$.fragment,n),$(s.$$.fragment,n),i=!0)},o(n){b(t.$$.fragment,n),b(s.$$.fragment,n),i=!1},d(n){n&&F(e),k(t,n),k(s,n)}}}function Ol(r){let t,e,s={triggerCharacter:"@",onMentionItemsUpdated:r[0],$$slots:{default:[Fl]},$$scope:{ctx:r}};return t=new Ni.Root({props:s}),r[9](t),{c(){E(t.$$.fragment)},m(i,n){T(t,i,n),e=!0},p(i,[n]){const o={};1&n&&(o.onMentionItemsUpdated=i[0]),262148&n&&(o.$$scope={dirty:n,ctx:i}),t.$set(o)},i(i){e||($(t.$$.fragment,i),e=!0)},o(i){b(t.$$.fragment,i),e=!1},d(i){r[9](null),k(t,i)}}}function Nl(r,t,e){let s,{requestEditorFocus:i}=t,{onMentionItemsUpdated:n}=t;const o=No("chatModel");if(!o)throw new Error("ChatModel not found in context");const a=new Qi(o,d),c=a.displayItems;let h;function d(m){return!!h&&(h.insertMention(m),a.closeDropdown(),!0)}function u(m){const p=a.selectMentionable(m);return i(),p}return Ve(r,c,m=>e(2,s=m)),Zi(()=>{a.dispose()}),r.$$set=m=>{"requestEditorFocus"in m&&e(6,i=m.requestEditorFocus),"onMentionItemsUpdated"in m&&e(0,n=m.onMentionItemsUpdated)},[n,h,s,c,function(m){m===void 0?a.closeDropdown():(a.openDropdown(),a.userQuery.set(m))},u,i,m=>d(m),m=>u(m),function(m){ss[m?"unshift":"push"](()=>{h=m,e(1,h)})}]}class Ll extends ke{constructor(t){super(),Ae(this,t,Nl,Ol,Fe,{requestEditorFocus:6,onMentionItemsUpdated:0,insertMentionNode:7})}get insertMentionNode(){return this.$$.ctx[7]}}function ur(r){let t,e,s,i,n,o,a,c,h,d,u,m,p,C,x={focusOnInit:!0,$$slots:{default:[Rl]},$$scope:{ctx:r}};return o=new Lr.Root({props:x}),r[25](o),h=new Ee({props:{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$slots:{default:[Dl]},$$scope:{ctx:r}}}),h.$on("click",function(){de(r[0].disposeDiffViewPanel)&&r[0].disposeDiffViewPanel.apply(this,arguments)}),u=new Ee({props:{id:"send",size:1,variant:"solid",color:"accent",title:r[3]===Te.instruction?"Instruct Augment":"Edit with Augment",disabled:!r[4].trim()||r[11],$$slots:{iconRight:[Ul],default:[ql]},$$scope:{ctx:r}}}),u.$on("click",r[14]),{c(){t=et("div"),e=J(),s=et("div"),i=et("div"),n=et("div"),E(o.$$.fragment),a=J(),c=et("div"),E(h.$$.fragment),d=J(),E(u.$$.fragment),X(n,"class","l-input-area__input svelte-1cxscce"),X(c,"class","c-instruction-drawer-panel__btn-container svelte-1cxscce"),X(i,"class","instruction-drawer-panel__contents svelte-1cxscce"),X(i,"tabindex","0"),X(i,"role","button"),X(s,"class","instruction-drawer-panel svelte-1cxscce"),ts(s,"top",r[5]+"px"),ts(s,"height",r[6]+"px")},m(S,y){O(S,t,y),O(S,e,y),O(S,s,y),st(s,i),st(i,n),T(o,n,null),r[26](n),st(i,a),st(i,c),T(h,c,null),st(c,d),T(u,c,null),m=!0,p||(C=[Cr(r[15].call(null,t)),Ie(i,"click",r[17]),Ie(i,"keydown",r[27])],p=!0)},p(S,y){r=S;const _={};1296&y[0]|256&y[1]&&(_.$$scope={dirty:y,ctx:r}),o.$set(_);const w={};256&y[1]&&(w.$$scope={dirty:y,ctx:r}),h.$set(w);const N={};8&y[0]&&(N.title=r[3]===Te.instruction?"Instruct Augment":"Edit with Augment"),2064&y[0]&&(N.disabled=!r[4].trim()||r[11]),8&y[0]|256&y[1]&&(N.$$scope={dirty:y,ctx:r}),u.$set(N),(!m||32&y[0])&&ts(s,"top",r[5]+"px"),(!m||64&y[0])&&ts(s,"height",r[6]+"px")},i(S){m||($(o.$$.fragment,S),$(h.$$.fragment,S),$(u.$$.fragment,S),m=!0)},o(S){b(o.$$.fragment,S),b(h.$$.fragment,S),b(u.$$.fragment,S),m=!1},d(S){S&&(F(t),F(e),F(s)),r[25](null),k(o),r[26](null),k(h),k(u),p=!1,Xi(C)}}}function Rl(r){let t,e,s,i,n,o,a,c;t=new va({props:{shortcuts:{Enter:r[23]}}});let h={requestEditorFocus:r[16],onMentionItemsUpdated:r[18]};return s=new Ll({props:h}),r[24](s),n=new Lr.Content({props:{content:r[4],onContentChanged:r[19]}}),a=new $a({props:{placeholder:r[10]}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment),i=J(),E(n.$$.fragment),o=J(),E(a.$$.fragment)},m(d,u){T(t,d,u),O(d,e,u),T(s,d,u),O(d,i,u),T(n,d,u),O(d,o,u),T(a,d,u),c=!0},p(d,u){s.$set({});const m={};16&u[0]&&(m.content=d[4]),n.$set(m);const p={};1024&u[0]&&(p.placeholder=d[10]),a.$set(p)},i(d){c||($(t.$$.fragment,d),$(s.$$.fragment,d),$(n.$$.fragment,d),$(a.$$.fragment,d),c=!0)},o(d){b(t.$$.fragment,d),b(s.$$.fragment,d),b(n.$$.fragment,d),b(a.$$.fragment,d),c=!1},d(d){d&&(F(e),F(i),F(o)),k(t,d),r[24](null),k(s,d),k(n,d),k(a,d)}}}function Dl(r){let t,e,s;return e=new Be({props:{keybinding:"esc"}}),{c(){t=ct(`Close
          `),E(e.$$.fragment)},m(i,n){O(i,t,n),T(e,i,n),s=!0},p:Z,i(i){s||($(e.$$.fragment,i),s=!0)},o(i){b(e.$$.fragment,i),s=!1},d(i){i&&F(t),k(e,i)}}}function ql(r){let t,e=r[3]===Te.instruction?"Instruct":"Edit";return{c(){t=ct(e)},m(s,i){O(s,t,i)},p(s,i){8&i[0]&&e!==(e=s[3]===Te.instruction?"Instruct":"Edit")&&Ht(t,e)},d(s){s&&F(t)}}}function Ul(r){let t,e;return t=new oc({props:{slot:"iconRight"}}),{c(){E(t.$$.fragment)},m(s,i){T(t,s,i),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Hl(r){let t,e,s=r[2]&&ur(r);return{c(){s&&s.c(),t=ie()},m(i,n){s&&s.m(i,n),O(i,t,n),e=!0},p(i,n){i[2]?s?(s.p(i,n),4&n[0]&&$(s,1)):(s=ur(i),s.c(),$(s,1),s.m(t.parentNode,t)):s&&(gt(),b(s,1,1,()=>{s=null}),_t())},i(i){e||($(s),e=!0)},o(i){b(s),e=!1},d(i){i&&F(t),s&&s.d(i)}}}function Pl(r,t,e){let s,i,n,o,a,c,h=Z,d=()=>(h(),h=he(m,U=>e(22,o=U)),m),u=Z;r.$$.on_destroy.push(()=>h()),r.$$.on_destroy.push(()=>u());let{diffViewModel:m}=t;d();let{initialConversation:p}=t,{initialFlags:C}=t;const x=Ca.getContext().monaco,S={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},y=new Ar(vs);let _=new Pr;y.registerConsumer(_);let w=new en(y,vs,_,{initialConversation:p,initialFlags:C});const N=w.currentConversationModel;let D,j;y.registerConsumer(w),function(U){Lo("chatModel",U)}(w);let z,W="";const P=m.mode;Ve(r,P,U=>e(3,a=U));const Y=m.selectionLines;function ot(){const U=m.getModifiedEditor(),Ne=It(x);if(!U||!Ne||(z==null||z.clear(),!n))return;const Ke=n.start,us=n.end,xs={range:new Ne.Range(Ke+1,1,us+1,1),options:S};z||(z=U.createDecorationsCollection()),z.set([xs])}function Tt(){return!!(W!=null&&W.trim())&&(m.handleInstructionSubmit(W),!0)}Ve(r,Y,U=>e(2,n=U)),xr(async()=>{await Ei(),Nt(),e(5,Pt=m.editorOffset)}),Zi(()=>{D==null||D.destroy(),z==null||z.clear()});let K,G,Pt=0,Oe=57;const Nt=()=>K==null?void 0:K.forceFocus();return r.$$set=U=>{"diffViewModel"in U&&d(e(0,m=U.diffViewModel)),"initialConversation"in U&&e(20,p=U.initialConversation),"initialFlags"in U&&e(21,C=U.initialFlags)},r.$$.update=()=>{if(8&r.$$.dirty[0]&&e(10,s=(a===Te.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs"),4194304&r.$$.dirty[0]&&(e(9,i=o.isLoading),u(),u=he(i,U=>e(11,c=U))),6&r.$$.dirty[0]&&j){if(n==null)e(6,Oe=0);else{const U=j.scrollHeight;e(6,Oe=Math.min(40+U,108))}D==null||D.update({heightInPx:Oe}),ot()}},[m,j,n,a,W,Pt,Oe,K,G,i,s,c,P,Y,Tt,function(U){if(U){const Ne=n?n.start:1;D=m.renderInstructionsDrawerViewZone(U,{line:Ne,heightInPx:Oe,onDomNodeTop:Ke=>{e(5,Pt=m.editorOffset+Ke)},autoFocus:!0}),ot()}},()=>K==null?void 0:K.requestFocus(),Nt,U=>{N.saveDraftMentions(U.current)},function(U){e(4,W=U.rawText)},p,C,o,()=>Tt(),function(U){ss[U?"unshift":"push"](()=>{G=U,e(8,G)})},function(U){ss[U?"unshift":"push"](()=>{K=U,e(7,K)})},function(U){ss[U?"unshift":"push"](()=>{j=U,e(1,j)})},U=>{U.key==="Enter"&&(Nt(),U.stopPropagation(),U.preventDefault())}]}class zl extends ke{constructor(t){super(),Ae(this,t,Pl,Hl,Fe,{diffViewModel:0,initialConversation:20,initialFlags:21},null,[-1,-1])}}const{window:Ii}=Ho;function dr(r,t,e){const s=r.slice();return s[17]=t[e],s[19]=e,s}function fr(r){let t,e,s,i,n;return e=new ic({props:{diffViewModel:r[3]}}),i=new zl({props:{diffViewModel:r[3]}}),{c(){t=et("div"),E(e.$$.fragment),s=J(),E(i.$$.fragment),X(t,"class","sticky-top svelte-453n6i")},m(o,a){O(o,t,a),T(e,t,null),O(o,s,a),T(i,o,a),n=!0},p(o,a){const c={};8&a&&(c.diffViewModel=o[3]),e.$set(c);const h={};8&a&&(h.diffViewModel=o[3]),i.$set(h)},i(o){n||($(e.$$.fragment,o),$(i.$$.fragment,o),n=!0)},o(o){b(e.$$.fragment,o),b(i.$$.fragment,o),n=!1},d(o){o&&(F(t),F(s)),k(e),k(i,o)}}}function mr(r){let t,e,s=Ks(r[4]),i=[];for(let o=0;o<s.length;o+=1)i[o]=gr(dr(r,s,o));const n=o=>b(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();t=ie()},m(o,a){for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(o,a);O(o,t,a),e=!0},p(o,a){if(280&a){let c;for(s=Ks(o[4]),c=0;c<s.length;c+=1){const h=dr(o,s,c);i[c]?(i[c].p(h,a),$(i[c],1)):(i[c]=gr(h),i[c].c(),$(i[c],1),i[c].m(t.parentNode,t))}for(gt(),c=s.length;c<i.length;c+=1)n(c);_t()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)$(i[a]);e=!0}},o(o){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)b(i[a]);e=!1},d(o){o&&F(t),Sr(i,o)}}}function pr(r){var n;let t,e;function s(){return r[14](r[17])}function i(){return r[15](r[17])}return t=new Ga({props:{isFocused:((n=r[3])==null?void 0:n.currFocusedChunkIdx)===r[19],onAccept:s,onReject:i,diffViewModel:r[3],leaf:r[17],align:"right",disableApply:r[8]}}),{c(){E(t.$$.fragment)},m(o,a){T(t,o,a),e=!0},p(o,a){var h;r=o;const c={};8&a&&(c.isFocused=((h=r[3])==null?void 0:h.currFocusedChunkIdx)===r[19]),24&a&&(c.onAccept=s),24&a&&(c.onReject=i),8&a&&(c.diffViewModel=r[3]),16&a&&(c.leaf=r[17]),256&a&&(c.disableApply=r[8]),t.$set(c)},i(o){e||($(t.$$.fragment,o),e=!0)},o(o){b(t.$$.fragment,o),e=!1},d(o){k(t,o)}}}function gr(r){let t,e,s=r[17].unitOfCodeWork.modifiedCode!==r[17].unitOfCodeWork.originalCode&&pr(r);return{c(){s&&s.c(),t=ie()},m(i,n){s&&s.m(i,n),O(i,t,n),e=!0},p(i,n){i[17].unitOfCodeWork.modifiedCode!==i[17].unitOfCodeWork.originalCode?s?(s.p(i,n),16&n&&$(s,1)):(s=pr(i),s.c(),$(s,1),s.m(t.parentNode,t)):s&&(gt(),b(s,1,1,()=>{s=null}),_t())},i(i){e||($(s),e=!0)},o(i){b(s),e=!1},d(i){i&&F(t),s&&s.d(i)}}}function jl(r){var h;let t,e,s,i,n,o,a=r[3]&&fr(r),c=r[3]&&((h=r[4])==null?void 0:h.length)&&!r[7]&&mr(r);return{c(){t=et("div"),a&&a.c(),e=J(),s=et("div"),i=et("div"),n=J(),c&&c.c(),X(i,"class","editor svelte-453n6i"),X(s,"class","editor-container svelte-453n6i"),X(t,"class","diff-view-container svelte-453n6i")},m(d,u){O(d,t,u),a&&a.m(t,null),st(t,e),st(t,s),st(s,i),r[13](i),st(s,n),c&&c.m(s,null),o=!0},p(d,u){var m;d[3]?a?(a.p(d,u),8&u&&$(a,1)):(a=fr(d),a.c(),$(a,1),a.m(t,e)):a&&(gt(),b(a,1,1,()=>{a=null}),_t()),d[3]&&((m=d[4])!=null&&m.length)&&!d[7]?c?(c.p(d,u),152&u&&$(c,1)):(c=mr(d),c.c(),$(c,1),c.m(s,null)):c&&(gt(),b(c,1,1,()=>{c=null}),_t())},i(d){o||($(a),$(c),o=!0)},o(d){b(a),b(c),o=!1},d(d){d&&F(t),a&&a.d(),r[13](null),c&&c.d()}}}function Wl(r){let t,e,s,i;return t=new ba.Root({props:{$$slots:{default:[jl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(n,o){T(t,n,o),e=!0,s||(i=[Ie(Ii,"message",function(){var a,c;de((a=r[0])==null?void 0:a.handleMessageFromExtension)&&((c=r[0])==null||c.handleMessageFromExtension.apply(this,arguments))}),Ie(Ii,"focus",r[11]),Ie(Ii,"blur",r[12])],s=!0)},p(n,[o]){r=n;const a={};1048986&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a)},i(n){e||($(t.$$.fragment,n),e=!0)},o(n){b(t.$$.fragment,n),e=!1},d(n){k(t,n),s=!1,Xi(i)}}}function Gl(r,t,e){let s,i,n,o,a,c,h,d,u,m,p=Z,C=Z,x=Z;function S(_){const w=Do.dark;return Sa((_==null?void 0:_.category)||w,_==null?void 0:_.intensity)??xa.get(w)}Ve(r,Ro,_=>e(10,a=_)),r.$$.on_destroy.push(()=>p()),r.$$.on_destroy.push(()=>C()),r.$$.on_destroy.push(()=>x()),xr(async()=>{e(9,m=await window.augmentDeps.monaco),m||console.error("Monaco not loaded. Diff view cannot be initialized.")}),Zi(()=>{d==null||d.dispose()});let y=!1;return r.$$.update=()=>{if(1539&r.$$.dirty&&m&&u&&!d&&(e(0,d=new Ua(u,S(a),m)),p(),p=he(d,_=>e(3,o=_))),1&r.$$.dirty&&(e(6,s=d==null?void 0:d.disableApply),x(),x=he(s,_=>e(8,h=_))),1&r.$$.dirty&&(e(5,i=d==null?void 0:d.disableResolution),C(),C=he(i,_=>e(7,c=_))),1025&r.$$.dirty){const _=a;d&&(d==null||d.updateTheme(S(_)))}8&r.$$.dirty&&e(4,n=o==null?void 0:o.leaves),5&r.$$.dirty&&(d==null||d.updateIsWebviewFocused(y))},[d,u,y,o,n,i,s,c,h,m,a,()=>e(2,y=!0),()=>e(2,y=!1),function(_){ss[_?"unshift":"push"](()=>{u=_,e(1,u)})},_=>o==null?void 0:o.acceptChunk(_),_=>o==null?void 0:o.rejectChunk(_)]}new class extends ke{constructor(r){super(),Ae(this,r,Gl,Wl,Fe,{})}}({target:document.getElementById("app")});
