<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agent Diff</title>
    <script nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <script type="module" crossorigin src="./assets/remote-agent-diff-EtmRTnyK.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-uKUHz-bK.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-DDX-Gvwz.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/index-CagAjuOI.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-CCbpeEq3.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-CQzh_Hae.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-D4p6YcQf.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-DdVtH9Vr.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/index-GYuo8qik.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-DGbwX4zn.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/index-Bcx5x-t6.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/index-D0JCd9Au.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-D5NvkEWZ.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-ffrJmKr6.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-n5HCoiGq.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/index-BBwB6w04.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BqjOeIg4.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-D5QDitBR.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-ggitH03G.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-CAgP5Fvb.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/types-DDm27S8B.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-DLmlSKam.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-Ba2-vrJ4.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-BSg_W9Au.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-DNGY17t7.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-CtjgW1F_.js" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-Dx3cBvPf.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-D5Z9LOF7.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/index-DEzma35W.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-FeoFGSYm.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-CbFzDG5Z.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-DTcQ2vsq.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-DokFokeT.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-DPofEGCN.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-zn72hvQy.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMsGeQ5J.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-diff-COoVj7ka.css" nonce="nonce-6TMv/0IxquwpfING1rBvkw==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
