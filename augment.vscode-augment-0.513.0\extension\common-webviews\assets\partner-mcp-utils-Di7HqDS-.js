var me=Object.defineProperty;var de=(s,e,t)=>e in s?me(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var l=(s,e,t)=>de(s,typeof e!="symbol"?e+"":e,t);import{S as ge}from"./IconButtonAugment-CQzh_Hae.js";import{S as C,i as _,s as w,J as G,_ as te,as as se,a as g,D as U,V as X,$ as ne,a0 as z,f as S,h as M,Q as N,K as j,L as Y,M as K,w as T,t as J,u as P,v as Q,j as A,k as u,T as be,I as Ce,am as _e,d as r,X as we,E as pe,F as ve,G as fe,W as k,b as d,n as c,H as D,y as R,z as H,A as F,e as f,B as V,l as y}from"./SpinnerAugment-uKUHz-bK.js";import{L as ae,G as O,H as ie}from"./index-DP6mqmYw.js";const C1=15,_1=1e3,ye=25e4,w1=2e4;class p1{constructor(e){l(this,"_enableEditableHistory",!1);l(this,"_enablePreferenceCollection",!1);l(this,"_enableRetrievalDataCollection",!1);l(this,"_enableDebugFeatures",!1);l(this,"_enableConversationDebugUtils",!1);l(this,"_enableRichTextHistory",!1);l(this,"_enableAgentSwarmMode",!1);l(this,"_modelDisplayNameToId",{});l(this,"_fullFeatured",!0);l(this,"_enableExternalSourcesInChat",!1);l(this,"_smallSyncThreshold",15);l(this,"_bigSyncThreshold",1e3);l(this,"_enableSmartPaste",!1);l(this,"_enableDirectApply",!1);l(this,"_summaryTitles",!1);l(this,"_suggestedEditsAvailable",!1);l(this,"_enableShareService",!1);l(this,"_maxTrackableFileCount",ye);l(this,"_enableDesignSystemRichTextEditor",!1);l(this,"_enableSources",!1);l(this,"_enableChatMermaidDiagrams",!1);l(this,"_smartPastePrecomputeMode",ge.visibleHover);l(this,"_useNewThreadsMenu",!1);l(this,"_enableChatMermaidDiagramsMinVersion",!1);l(this,"_enablePromptEnhancer",!1);l(this,"_idleNewSessionNotificationTimeoutMs");l(this,"_idleNewSessionMessageTimeoutMs");l(this,"_enableChatMultimodal",!1);l(this,"_enableAgentMode",!1);l(this,"_enableAgentAutoMode",!1);l(this,"_enableRichCheckpointInfo",!1);l(this,"_agentMemoriesFilePathName");l(this,"_conversationHistorySizeThresholdBytes",44040192);l(this,"_userTier","unknown");l(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});l(this,"_truncateChatHistory",!1);l(this,"_enableBackgroundAgents",!1);l(this,"_enableNewThreadsList",!1);l(this,"_customPersonalityPrompts",{});l(this,"_enablePersonalities",!1);l(this,"_enableRules",!1);l(this,"_memoryClassificationOnFirstToken",!1);l(this,"_enableGenerateCommitMessage",!1);l(this,"_modelRegistry",{});l(this,"_enableModelRegistry",!1);l(this,"_enableTaskList",!1);l(this,"_clientAnnouncement","");l(this,"_useHistorySummary",!1);l(this,"_historySummaryParams","");l(this,"_enableExchangeStorage",!1);l(this,"_enableToolUseStateStorage",!1);l(this,"_retryChatStreamTimeouts",!1);l(this,"_enableCommitIndexing",!1);l(this,"_enableMemoryRetrieval",!1);l(this,"_enableAgentTabs",!1);l(this,"_isVscodeVersionOutdated",!1);l(this,"_vscodeMinVersion","");l(this,"_enableGroupedTools",!1);l(this,"_remoteAgentsResumeHintAvailableTtlDays",0);l(this,"_enableParallelTools",!1);l(this,"_enableAgentGitTracker",!1);l(this,"_memoriesParams",{});l(this,"_subscribers",new Set);l(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));l(this,"update",e=>{this._enableEditableHistory=e.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=e.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=e.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=e.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=e.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=e.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=e.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...e.modelDisplayNameToId},this._fullFeatured=e.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=e.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=e.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=e.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=e.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=e.enableDirectApply??this._enableDirectApply,this._summaryTitles=e.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=e.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=e.enableShareService??this._enableShareService,this._maxTrackableFileCount=e.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=e.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=e.enableSources??this._enableSources,this._enableChatMermaidDiagrams=e.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=e.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=e.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=e.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=e.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=e.idleNewSessionMessageTimeoutMs??(e.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=e.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=e.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=e.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=e.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=e.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=e.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=e.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=e.userTier??this._userTier,this._eloModelConfiguration=e.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=e.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=e.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=e.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=e.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=e.enablePersonalities??this._enablePersonalities,this._enableRules=e.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=e.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._enableGenerateCommitMessage=e.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=e.modelRegistry??this._modelRegistry,this._enableModelRegistry=e.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=e.enableTaskList??this._enableTaskList,this._clientAnnouncement=e.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=e.useHistorySummary??this._useHistorySummary,this._historySummaryParams=e.historySummaryParams??this._historySummaryParams,this._enableExchangeStorage=e.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=e.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=e.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=e.enableMemoryRetrieval??this._enableMemoryRetrieval,this._enableAgentTabs=e.enableAgentTabs??this._enableAgentTabs,this._isVscodeVersionOutdated=e.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=e.vscodeMinVersion??this._vscodeMinVersion,this._enableGroupedTools=e.enableGroupedTools??this._enableGroupedTools,this._remoteAgentsResumeHintAvailableTtlDays=e.remoteAgentsResumeHintAvailableTtlDays??this._remoteAgentsResumeHintAvailableTtlDays,this._enableToolUseStateStorage=e.enableToolUseStateStorage??this._enableToolUseStateStorage,this._enableParallelTools=e.enableParallelTools??this._enableParallelTools,this._enableAgentGitTracker=e.enableAgentGitTracker??this._enableAgentGitTracker,this._memoriesParams=e.memoriesParams??this._memoriesParams,this._subscribers.forEach(t=>t(this))});l(this,"isModelIdValid",e=>e!==void 0&&(Object.values(this._modelDisplayNameToId).includes(e)||Object.values(this._modelRegistry).includes(e??"")));l(this,"getModelDisplayName",e=>{if(e!==void 0)return Object.keys(this._modelDisplayNameToId).find(t=>this._modelDisplayNameToId[t]===e)});e&&this.update(e)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((e,t)=>{const a=e.toLowerCase(),i=t.toLowerCase();return a==="default"&&i!=="default"?-1:i==="default"&&a!=="default"?1:e.localeCompare(t)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary}get historySummaryParams(){return this._historySummaryParams}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get enableAgentTabs(){return this._enableAgentTabs}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}get enableGroupedTools(){return this._enableGroupedTools}get remoteAgentsResumeHintAvailableTtlDays(){return this._remoteAgentsResumeHintAvailableTtlDays}get enableParallelTools(){return this._enableParallelTools}get enableAgentGitTracker(){return this._enableAgentGitTracker}get memoriesParams(){return this._memoriesParams}}class W{constructor(e){this._opts=e}get color(){return this._opts.color}get size(){return this._opts.size??1}get variant(){return this._opts.variant}}l(W,"CONTEXT_KEY","augment-badge");const Me=s=>({}),oe=s=>({}),xe=s=>({}),re=s=>({}),Te=s=>({}),le=s=>({});function ce(s){let e,t;const a=s[8].leftButtons,i=G(a,s,s[17],re);return{c(){e=U("div"),i&&i.c(),r(e,"class","c-badge__left-buttons svelte-1bpun12")},m(n,o){S(n,e,o),i&&i.m(e,null),t=!0},p(n,o){i&&i.p&&(!t||131072&o)&&j(i,a,n,n[17],t?K(a,n[17],o,xe):Y(n[17]),re)},i(n){t||(T(i,n),t=!0)},o(n){P(i,n),t=!1},d(n){n&&u(e),i&&i.d(n)}}}function he(s){let e,t;return e=new we({props:{size:s[6],weight:"medium",$$slots:{default:[Se]},$$scope:{ctx:s}}}),{c(){pe(e.$$.fragment)},m(a,i){ve(e,a,i),t=!0},p(a,i){const n={};131072&i&&(n.$$scope={dirty:i,ctx:a}),e.$set(n)},i(a){t||(T(e.$$.fragment,a),t=!0)},o(a){P(e.$$.fragment,a),t=!1},d(a){fe(e,a)}}}function Se(s){let e,t;const a=s[8].default,i=G(a,s,s[17],null);return{c(){e=U("div"),i&&i.c(),r(e,"class","c-badge-body svelte-1bpun12")},m(n,o){S(n,e,o),i&&i.m(e,null),t=!0},p(n,o){i&&i.p&&(!t||131072&o)&&j(i,a,n,n[17],t?K(a,n[17],o,null):Y(n[17]),null)},i(n){t||(T(i,n),t=!0)},o(n){P(i,n),t=!1},d(n){n&&u(e),i&&i.d(n)}}}function ue(s){let e,t;const a=s[8].rightButtons,i=G(a,s,s[17],oe);return{c(){e=U("div"),i&&i.c(),r(e,"class","c-badge__right-buttons svelte-1bpun12")},m(n,o){S(n,e,o),i&&i.m(e,null),t=!0},p(n,o){i&&i.p&&(!t||131072&o)&&j(i,a,n,n[17],t?K(a,n[17],o,Me):Y(n[17]),oe)},i(n){t||(T(i,n),t=!0)},o(n){P(i,n),t=!1},d(n){n&&u(e),i&&i.d(n)}}}function Le(s){let e,t,a,i,n,o,E,B;const I=s[8].chaser,x=G(I,s,s[17],le);let b=s[7].leftButtons&&ce(s),p=s[7].default&&he(s),v=s[7].rightButtons&&ue(s),h=[te(s[0]),se(s[3]),{class:n=`c-badge c-badge--${s[0]} c-badge--${s[1]} c-badge--size-${s[2]}`},{role:"button"},{tabindex:"0"}],Z={};for(let m=0;m<h.length;m+=1)Z=g(Z,h[m]);return{c(){e=U("div"),x&&x.c(),t=X(),b&&b.c(),a=X(),p&&p.c(),i=X(),v&&v.c(),ne(e,Z),z(e,"c-badge--highContrast",s[4]),z(e,"c-badge--inset",s[5]),z(e,"svelte-1bpun12",!0)},m(m,L){S(m,e,L),x&&x.m(e,null),M(e,t),b&&b.m(e,null),M(e,a),p&&p.m(e,null),M(e,i),v&&v.m(e,null),o=!0,E||(B=[N(e,"click",s[9]),N(e,"keydown",s[10]),N(e,"keyup",s[11]),N(e,"mousedown",s[12]),N(e,"mouseover",s[13]),N(e,"focus",s[14]),N(e,"mouseleave",s[15]),N(e,"blur",s[16])],E=!0)},p(m,[L]){x&&x.p&&(!o||131072&L)&&j(x,I,m,m[17],o?K(I,m[17],L,Te):Y(m[17]),le),m[7].leftButtons?b?(b.p(m,L),128&L&&T(b,1)):(b=ce(m),b.c(),T(b,1),b.m(e,a)):b&&(J(),P(b,1,1,()=>{b=null}),Q()),m[7].default?p?(p.p(m,L),128&L&&T(p,1)):(p=he(m),p.c(),T(p,1),p.m(e,i)):p&&(J(),P(p,1,1,()=>{p=null}),Q()),m[7].rightButtons?v?(v.p(m,L),128&L&&T(v,1)):(v=ue(m),v.c(),T(v,1),v.m(e,null)):v&&(J(),P(v,1,1,()=>{v=null}),Q()),ne(e,Z=A(h,[1&L&&te(m[0]),8&L&&se(m[3]),(!o||7&L&&n!==(n=`c-badge c-badge--${m[0]} c-badge--${m[1]} c-badge--size-${m[2]}`))&&{class:n},{role:"button"},{tabindex:"0"}])),z(e,"c-badge--highContrast",m[4]),z(e,"c-badge--inset",m[5]),z(e,"svelte-1bpun12",!0)},i(m){o||(T(x,m),T(b),T(p),T(v),o=!0)},o(m){P(x,m),P(b),P(p),P(v),o=!1},d(m){m&&u(e),x&&x.d(m),b&&b.d(),p&&p.d(),v&&v.d(),E=!1,be(B)}}}function Pe(s,e,t){let{$$slots:a={},$$scope:i}=e;const n=Ce(a);let{color:o="accent"}=e,{variant:E="soft"}=e,{size:B=1}=e,{radius:I="medium"}=e,{highContrast:x=!1}=e,{inset:b=!1}=e;const p=B===0?0:B===3?2:1,v=new W({color:o,size:B,variant:E});return _e(W.CONTEXT_KEY,v),s.$$set=h=>{"color"in h&&t(0,o=h.color),"variant"in h&&t(1,E=h.variant),"size"in h&&t(2,B=h.size),"radius"in h&&t(3,I=h.radius),"highContrast"in h&&t(4,x=h.highContrast),"inset"in h&&t(5,b=h.inset),"$$scope"in h&&t(17,i=h.$$scope)},[o,E,B,I,x,b,p,n,a,function(h){k.call(this,s,h)},function(h){k.call(this,s,h)},function(h){k.call(this,s,h)},function(h){k.call(this,s,h)},function(h){k.call(this,s,h)},function(h){k.call(this,s,h)},function(h){k.call(this,s,h)},function(h){k.call(this,s,h)},i]}class v1 extends C{constructor(e){super(),_(this,e,Pe,Le,w,{color:0,variant:1,size:2,radius:3,highContrast:4,inset:5})}}function Ae(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"fill-rule","evenodd"),r(t,"clip-rule","evenodd"),r(t,"d","M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H7V5.25C7 4.97386 7.22386 4.75 7.5 4.75C7.77614 4.75 8 4.97386 8 5.25V7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H8V9.75C8 10.0261 7.77614 10.25 7.5 10.25C7.22386 10.25 7 10.0261 7 9.75V8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z"),r(t,"fill","currentColor"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class f1 extends C{constructor(e){super(),_(this,e,null,Ae,w,{})}}function De(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m113-303c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-111 111-47-47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0z"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function Re(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class y1 extends C{constructor(e){super(),_(this,e,Re,De,w,{})}}function He(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m-81-337c-9.4 9.4-9.4 24.6 0 33.9l47 47-47 47c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l47-47 47 47c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-47-47 47-47c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-47 47-47-47c-9.4-9.4-24.6-9.4-33.9 0"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function Fe(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class M1 extends C{constructor(e){super(),_(this,e,Fe,He,w,{})}}function Ve(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M320 464c8.8 0 16-7.2 16-16V160h-80c-17.7 0-32-14.3-32-32V48H64c-8.8 0-16 7.2-16 16v384c0 8.8 7.2 16 16 16zM0 64C0 28.7 28.7 0 64 0h165.5c17 0 33.3 6.7 45.3 18.7l90.5 90.5c12 12 18.7 28.3 18.7 45.3V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64z"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function Ee(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class x1 extends C{constructor(e){super(),_(this,e,Ee,Ve,w,{})}}function Be(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"fill-rule","evenodd"),r(t,"clip-rule","evenodd"),r(t,"d","M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"),r(t,"fill","currentColor"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class T1 extends C{constructor(e){super(),_(this,e,null,Be,w,{})}}function Ne(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"fill-rule","evenodd"),r(t,"clip-rule","evenodd"),r(t,"d","M1.84998 7.49998C1.84998 4.66458 4.05979 1.84998 7.49998 1.84998C10.2783 1.84998 11.6515 3.9064 12.2367 5H10.5C10.2239 5 10 5.22386 10 5.5C10 5.77614 10.2239 6 10.5 6H13.5C13.7761 6 14 5.77614 14 5.5V2.5C14 2.22386 13.7761 2 13.5 2C13.2239 2 13 2.22386 13 2.5V4.31318C12.2955 3.07126 10.6659 0.849976 7.49998 0.849976C3.43716 0.849976 0.849976 4.18537 0.849976 7.49998C0.849976 10.8146 3.43716 14.15 7.49998 14.15C9.44382 14.15 11.0622 13.3808 12.2145 12.2084C12.8315 11.5806 13.3133 10.839 13.6418 10.0407C13.7469 9.78536 13.6251 9.49315 13.3698 9.38806C13.1144 9.28296 12.8222 9.40478 12.7171 9.66014C12.4363 10.3425 12.0251 10.9745 11.5013 11.5074C10.5295 12.4963 9.16504 13.15 7.49998 13.15C4.05979 13.15 1.84998 10.3354 1.84998 7.49998Z"),r(t,"fill","currentColor"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class S1 extends C{constructor(e){super(),_(this,e,null,Ne,w,{})}}var q=(s=>(s.shell="shell",s.webFetch="web-fetch",s.strReplaceEditor="str-replace-editor",s.codebaseRetrieval="codebase-retrieval",s.removeFiles="remove-files",s.remember="remember",s.view="view",s.saveFile="save-file",s.viewTaskList="view_tasklist",s.viewRangeUntruncated="view-range-untruncated",s.searchUntruncated="search-untruncated",s.reorganizeTaskList="reorganize_tasklist",s.updateTasks="update_tasks",s.addTasks="add_tasks",s.renderMermaid="render-mermaid",s.grepSearch="grep-search",s))(q||{}),ee=(s=>(s.webSearchV1="google_search",s.webSearch="web-search",s.gitHub="github-api",s.linear="linear",s.notion="notion",s.jira="jira",s.confluence="confluence",s.supabase="supabase",s.glean="glean",s.mcp="mcp",s))(ee||{});const ke=new Set(ae?Object.values(ae):[]),$e=new Set(ee?Object.values(ee):[]),Ie=new Set(q?Object.values(q):[]),ze=new Set([...ke,...$e,...Ie]);function L1(s){return ze.has(s)}function Ze(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function Oe(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class P1 extends C{constructor(e){super(),_(this,e,Oe,Ze,w,{})}}function Ge(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function Ue(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class A1 extends C{constructor(e){super(),_(this,e,Ue,Ge,w,{})}}function je(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 24c0 13.3 10.7 24 24 24h102.1L207 271c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l223-223L464 184c0 13.3 10.7 24 24 24s24-10.7 24-24V24c0-13.3-10.7-24-24-24H328c-13.3 0-24 10.7-24 24M72 32C32.2 32 0 64.2 0 104v336c0 39.8 32.2 72 72 72h336c39.8 0 72-32.2 72-72V312c0-13.3-10.7-24-24-24s-24 10.7-24 24v128c0 13.3-10.7 24-24 24H72c-13.3 0-24-10.7-24-24V104c0-13.3 10.7-24 24-24h128c13.3 0 24-10.7 24-24s-10.7-24-24-24z"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function Ye(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class D1 extends C{constructor(e){super(),_(this,e,Ye,je,w,{})}}function Ke(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M144 80v96H48V80zM48 32C21.5 32 0 53.5 0 80v96c0 26.5 21.5 48 48 48h24v96c0 48.6 39.4 88 88 88h96v24c0 26.5 21.5 48 48 48h96c26.5 0 48-21.5 48-48v-96c0-26.5-21.5-48-48-48h-96c-26.5 0-48 21.5-48 48v24h-96c-22.1 0-40-17.9-40-40v-96h24c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zm352 304v96h-96v-96z"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function Xe(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class R1 extends C{constructor(e){super(),_(this,e,Xe,Ke,w,{})}}function Je(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M177.1 48h93.7c2.7 0 5.2 1.3 6.7 3.6l19 28.4h-145l19-28.4c1.5-2.2 4-3.6 6.7-3.6zm177.1 32-36.7-55.1C307.1 9.4 289.6 0 270.9 0h-93.8c-18.7 0-36.2 9.4-46.6 24.9L93.8 80H24C10.7 80 0 90.7 0 104s10.7 24 24 24h11.6l24 324.7c2.5 33.4 30.3 59.3 63.8 59.3h201.1c33.5 0 61.3-25.9 63.8-59.3L412.4 128H424c13.3 0 24-10.7 24-24s-10.7-24-24-24h-56.1zm10.1 48-23.8 321.2c-.6 8.4-7.6 14.8-16 14.8H123.4c-8.4 0-15.3-6.5-16-14.8L83.7 128z"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function Qe(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class H1 extends C{constructor(e){super(),_(this,e,Qe,Je,w,{})}}function We(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"d","M10 6.5C10 8.433 8.433 10 6.5 10C4.567 10 3 8.433 3 6.5C3 4.567 4.567 3 6.5 3C8.433 3 10 4.567 10 6.5ZM9.30884 10.0159C8.53901 10.6318 7.56251 11 6.5 11C4.01472 11 2 8.98528 2 6.5C2 4.01472 4.01472 2 6.5 2C8.98528 2 11 4.01472 11 6.5C11 7.56251 10.6318 8.53901 10.0159 9.30884L12.8536 12.1464C13.0488 12.3417 13.0488 12.6583 12.8536 12.8536C12.6583 13.0488 12.3417 13.0488 12.1464 12.8536L9.30884 10.0159Z"),r(t,"fill","currentColor"),r(t,"fill-rule","evenodd"),r(t,"clip-rule","evenodd"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class F1 extends C{constructor(e){super(),_(this,e,null,We,w,{})}}function qe(s){let e,t,a,i,n;return{c(){e=d("svg"),t=d("path"),a=d("path"),i=d("path"),n=d("path"),r(t,"d","M7.49996 1.80002C4.35194 1.80002 1.79996 4.352 1.79996 7.50002C1.79996 10.648 4.35194 13.2 7.49996 13.2C10.648 13.2 13.2 10.648 13.2 7.50002C13.2 4.352 10.648 1.80002 7.49996 1.80002ZM0.899963 7.50002C0.899963 3.85494 3.85488 0.900024 7.49996 0.900024C11.145 0.900024 14.1 3.85494 14.1 7.50002C14.1 11.1451 11.145 14.1 7.49996 14.1C3.85488 14.1 0.899963 11.1451 0.899963 7.50002Z"),r(t,"fill","currentColor"),r(t,"fill-rule","evenodd"),r(t,"clip-rule","evenodd"),r(a,"d","M13.4999 7.89998H1.49994V7.09998H13.4999V7.89998Z"),r(a,"fill","currentColor"),r(a,"fill-rule","evenodd"),r(a,"clip-rule","evenodd"),r(i,"d","M7.09991 13.5V1.5H7.89991V13.5H7.09991zM10.375 7.49998C10.375 5.32724 9.59364 3.17778 8.06183 1.75656L8.53793 1.24341C10.2396 2.82218 11.075 5.17273 11.075 7.49998 11.075 9.82724 10.2396 12.1778 8.53793 13.7566L8.06183 13.2434C9.59364 11.8222 10.375 9.67273 10.375 7.49998zM3.99969 7.5C3.99969 5.17611 4.80786 2.82678 6.45768 1.24719L6.94177 1.75281C5.4582 3.17323 4.69969 5.32389 4.69969 7.5 4.6997 9.67611 5.45822 11.8268 6.94179 13.2472L6.45769 13.7528C4.80788 12.1732 3.9997 9.8239 3.99969 7.5z"),r(i,"fill","currentColor"),r(i,"fill-rule","evenodd"),r(i,"clip-rule","evenodd"),r(n,"d","M7.49996 3.95801C9.66928 3.95801 11.8753 4.35915 13.3706 5.19448 13.5394 5.28875 13.5998 5.50197 13.5055 5.67073 13.4113 5.83948 13.198 5.89987 13.0293 5.8056 11.6794 5.05155 9.60799 4.65801 7.49996 4.65801 5.39192 4.65801 3.32052 5.05155 1.97064 5.8056 1.80188 5.89987 1.58866 5.83948 1.49439 5.67073 1.40013 5.50197 1.46051 5.28875 1.62927 5.19448 3.12466 4.35915 5.33063 3.95801 7.49996 3.95801zM7.49996 10.85C9.66928 10.85 11.8753 10.4488 13.3706 9.6135 13.5394 9.51924 13.5998 9.30601 13.5055 9.13726 13.4113 8.9685 13.198 8.90812 13.0293 9.00238 11.6794 9.75643 9.60799 10.15 7.49996 10.15 5.39192 10.15 3.32052 9.75643 1.97064 9.00239 1.80188 8.90812 1.58866 8.9685 1.49439 9.13726 1.40013 9.30601 1.46051 9.51924 1.62927 9.6135 3.12466 10.4488 5.33063 10.85 7.49996 10.85z"),r(n,"fill","currentColor"),r(n,"fill-rule","evenodd"),r(n,"clip-rule","evenodd"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(o,E){S(o,e,E),M(e,t),M(e,a),M(e,i),M(e,n)},p:c,i:c,o:c,d(o){o&&u(e)}}}class V1 extends C{constructor(e){super(),_(this,e,null,qe,w,{})}}function e1(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"d","M1.17156 9.61319C1.14041 9.4804 1.2986 9.39676 1.39505 9.49321L6.50679 14.6049C6.60323 14.7014 6.5196 14.8596 6.38681 14.8284C3.80721 14.2233 1.77669 12.1928 1.17156 9.61319ZM1.00026 7.56447C0.997795 7.60413 1.01271 7.64286 1.0408 7.67096L8.32904 14.9592C8.35714 14.9873 8.39586 15.0022 8.43553 14.9997C8.76721 14.9791 9.09266 14.9353 9.41026 14.8701C9.51729 14.8481 9.55448 14.7166 9.47721 14.6394L1.36063 6.52279C1.28337 6.44552 1.15187 6.48271 1.12989 6.58974C1.06466 6.90734 1.02092 7.23278 1.00026 7.56447ZM1.58953 5.15875C1.56622 5.21109 1.57809 5.27224 1.6186 5.31275L10.6872 14.3814C10.7278 14.4219 10.7889 14.4338 10.8412 14.4105C11.0913 14.2991 11.3336 14.1735 11.5672 14.0347C11.6445 13.9888 11.6564 13.8826 11.5929 13.819L2.18099 4.40714C2.11742 4.34356 2.01121 4.35549 1.96529 4.43278C1.8265 4.66636 1.70091 4.9087 1.58953 5.15875ZM2.77222 3.53036C2.7204 3.47854 2.7172 3.39544 2.76602 3.34079C4.04913 1.9043 5.9156 1 7.99327 1C11.863 1 15 4.13702 15 8.00673C15 10.0844 14.0957 11.9509 12.6592 13.234C12.6046 13.2828 12.5215 13.2796 12.4696 13.2278L2.77222 3.53036Z"),r(t,"fill","currentColor"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class E1 extends C{constructor(e){super(),_(this,e,null,e1,w,{})}}function t1(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"d","M3.47498 3.32462C3.92288 3.68848 4.0909 3.66071 4.93192 3.60461L12.8609 3.12851C13.029 3.12851 12.8892 2.96075 12.8332 2.93286L11.5163 1.98091C11.264 1.78502 10.9278 1.56068 10.2835 1.6168L2.60594 2.17678C2.32595 2.20454 2.27001 2.34453 2.38153 2.45676L3.47498 3.32462ZM3.95103 5.17244V13.5151C3.95103 13.9634 4.17508 14.1312 4.67938 14.1035L13.3933 13.5992C13.8978 13.5715 13.954 13.263 13.954 12.8989V4.61222C13.954 4.24858 13.8142 4.05248 13.5053 4.08047L4.39915 4.61222C4.06311 4.64046 3.95103 4.80855 3.95103 5.17244ZM12.5534 5.61996C12.6093 5.87218 12.5534 6.12417 12.3007 6.15251L11.8808 6.23616V12.3952C11.5163 12.5911 11.1801 12.7031 10.9 12.7031C10.4516 12.7031 10.3392 12.5631 10.0033 12.1433L7.257 7.83198V12.0034L8.12602 12.1995C8.12602 12.1995 8.12602 12.7031 7.4249 12.7031L5.49203 12.8152C5.43588 12.7031 5.49203 12.4235 5.68808 12.3673L6.19248 12.2276V6.71226L5.49215 6.65615C5.43599 6.40392 5.57587 6.04029 5.96841 6.01205L8.04196 5.87229L10.9 10.2398V6.37615L10.1713 6.29251C10.1154 5.98418 10.3392 5.76029 10.6195 5.73252L12.5534 5.61996ZM1.96131 1.42092L9.94726 0.832827C10.928 0.748715 11.1803 0.805058 11.7967 1.25281L14.3458 3.04451C14.7665 3.35262 14.9067 3.4365 14.9067 3.77237V13.5992C14.9067 14.215 14.6823 14.5793 13.8979 14.635L4.6239 15.1951C4.03509 15.2231 3.75485 15.1392 3.4465 14.747L1.56922 12.3113C1.23284 11.863 1.09296 11.5276 1.09296 11.1351V2.40043C1.09296 1.89679 1.31736 1.47669 1.96131 1.42092Z"),r(t,"fill","currentColor"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class B1 extends C{constructor(e){super(),_(this,e,null,t1,w,{})}}function s1(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"fill-rule","evenodd"),r(t,"clip-rule","evenodd"),r(t,"d","M7.94136 1.60409C7.93274 1.02899 7.20756 0.782175 6.8499 1.23262L1.4527 8.03004C0.815662 8.83235 1.38702 10.0156 2.41149 10.0156H7.99303L8.05866 14.3959C8.06727 14.971 8.79245 15.2178 9.1501 14.7674L14.5473 7.96997C15.1843 7.16766 14.613 5.98443 13.5885 5.98443H7.97012L7.94136 1.60409Z"),r(t,"fill","currentColor"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class N1 extends C{constructor(e){super(),_(this,e,null,s1,w,{})}}function n1(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"fill-rule","evenodd"),r(t,"clip-rule","evenodd"),r(t,"d","M8.95113 2.67649L10.2775 1L11.887 2.24179L10.5704 3.906C11.25 4.70862 11.6591 5.74239 11.6591 6.87053C11.6591 9.42425 9.56277 11.4945 6.9768 11.4945C4.39084 11.4945 2.29451 9.42425 2.29451 6.87053C2.29451 4.31677 4.39084 2.24655 6.9768 2.24655C7.68222 2.24655 8.35119 2.4006 8.95113 2.67649ZM6.9768 9.50853C5.50146 9.50853 4.30546 8.32747 4.30546 6.87053C4.30546 5.41358 5.50146 4.23245 6.9768 4.23245C8.45215 4.23245 9.64814 5.41358 9.64814 6.87053C9.64814 8.32747 8.45215 9.50853 6.9768 9.50853ZM11.7135 10.8261C11.5975 10.9618 11.4753 11.0913 11.3477 11.2173C11.2202 11.3424 11.0873 11.4622 10.949 11.5758C10.8116 11.6894 10.6689 11.7969 10.5208 11.8982C10.3736 11.9995 10.2211 12.0955 10.065 12.1837C9.90978 12.2726 9.75012 12.3537 9.58684 12.4286C9.42448 12.5034 9.25856 12.5712 9.08906 12.6311C8.92046 12.6919 8.74919 12.7448 8.57434 12.7898C8.40217 12.8365 8.22645 12.8743 8.04892 12.9043C7.87319 12.9351 7.69478 12.958 7.51459 12.973C7.33706 12.988 7.15776 12.9959 6.97667 12.9959C6.79558 12.9959 6.61628 12.988 6.43876 12.973C6.25856 12.958 6.08016 12.9351 5.90441 12.9043C5.7269 12.8743 5.55116 12.8365 5.379 12.7898L4.85357 14.726C5.08194 14.7868 5.31565 14.838 5.55206 14.8784C5.78488 14.919 6.02217 14.9498 6.26213 14.9692C6.49763 14.9895 6.73582 15 6.97667 15C7.21753 15 7.4557 14.9895 7.69121 14.9692C7.93117 14.9498 8.16756 14.919 8.40129 14.8784C8.63768 14.838 8.87051 14.7868 9.09977 14.726C9.33171 14.6662 9.56007 14.5957 9.7831 14.5147C10.0088 14.4345 10.2291 14.3446 10.445 14.2451C10.6617 14.1455 10.8741 14.0372 11.0802 13.92C11.2871 13.802 11.4887 13.6751 11.684 13.5394C11.8803 13.4047 12.0704 13.2619 12.2532 13.1104C12.437 12.9589 12.6136 12.8003 12.7822 12.6338C12.9517 12.4673 13.1131 12.2946 13.2675 12.1141C13.4218 11.9343 13.5681 11.7467 13.7055 11.5538L12.0435 10.4041C11.9401 10.5495 11.8295 10.6905 11.7135 10.8261Z"),r(t,"fill","currentColor"),r(e,"width","16"),r(e,"height","16"),r(e,"viewBox","0 0 16 16"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class k1 extends C{constructor(e){super(),_(this,e,null,n1,w,{})}}function a1(s){let e,t;return{c(){e=d("svg"),t=d("path"),r(t,"d","M10.1449 2.40505C9.57467 1.83483 8.65013 1.8348 8.07986 2.40506L8.07985 2.40506L2.57309 7.91182C2.38301 8.1019 2.07483 8.1019 1.88475 7.91182C1.69467 7.72174 1.69467 7.41356 1.88475 7.22348L7.39151 1.71671L7.39152 1.71671C8.34193 0.76632 9.88283 0.766292 10.8332 1.7167C11.3962 2.27964 11.6257 3.04975 11.5217 3.78162C12.2536 3.67767 13.0238 3.90716 13.5867 4.4701L13.6154 4.49878L13.6155 4.49887C14.5658 5.44928 14.5658 6.99014 13.6154 7.94051L8.63502 12.9209C8.57168 12.9843 8.57166 13.087 8.63501 13.1503L9.65768 14.173C9.84775 14.3631 9.84774 14.6713 9.65765 14.8614C9.46757 15.0514 9.15938 15.0514 8.96931 14.8614L7.94669 13.8387C7.50317 13.3952 7.50315 12.6761 7.94667 12.2326L12.927 7.25217C13.4973 6.68194 13.4973 5.75739 12.927 5.18714L12.8984 5.15846L12.8983 5.15838C12.328 4.58821 11.4035 4.58824 10.8333 5.15846L6.73192 9.25986L6.72597 9.26572L6.67449 9.3172C6.48441 9.50728 6.17622 9.50729 5.98614 9.3172C5.79606 9.12712 5.79606 8.81894 5.98614 8.62886L10.1449 4.4701L10.1449 4.47009C10.7151 3.89984 10.7151 2.9753 10.1449 2.40507L10.1449 2.40505ZM9.45664 3.78176C9.64672 3.59168 9.64672 3.2835 9.45664 3.09341C9.26656 2.90333 8.95838 2.90333 8.7683 3.09341L4.69557 7.16612L4.69556 7.16613C3.74515 8.11652 3.74517 9.65744 4.69556 10.6079L4.69557 10.6079C5.64598 11.5582 7.18687 11.5582 8.13728 10.6079L8.13729 10.6079L12.21 6.53514C12.4001 6.34506 12.4001 6.03688 12.21 5.8468C12.0199 5.65671 11.7118 5.65671 11.5217 5.8468L7.44896 9.91952L7.44895 9.91953C6.8787 10.4897 5.95416 10.4897 5.38392 9.91953C4.81365 9.34925 4.81367 8.42469 5.3839 7.85448L5.38391 7.85448L9.45664 3.78176Z"),r(t,"fill","currentColor"),r(e,"width","15"),r(e,"height","15"),r(e,"viewBox","0 0 15 15"),r(e,"fill","none"),r(e,"xmlns","http://www.w3.org/2000/svg")},m(a,i){S(a,e,i),M(e,t)},p:c,i:c,o:c,d(a){a&&u(e)}}}class $1 extends C{constructor(e){super(),_(this,e,null,a1,w,{})}}var $=(s=>(s.STRIPE="augment-partner-remote-mcp-stripe",s.SENTRY="augment-partner-remote-mcp-sentry",s))($||{});function i1(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M155.3 154.6c0-22.3 18.6-30.9 48.4-30.9 43.4 0 98.5 13.3 141.9 36.7V26.1C298.3 7.2 251.1 0 203.8 0 88.1 0 11 60.4 11 161.4c0 157.9 216.8 132.3 216.8 200.4 0 26.4-22.9 34.9-54.7 34.9-47.2 0-108.2-19.5-156.1-45.5v128.5a396.09 396.09 0 0 0 156 32.4c118.6 0 200.3-51 200.3-153.6 0-170.2-218-139.7-218-203.9"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function o1(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}O.Safe,ie.mcpHost,O.Safe,O.Safe,ie.mcpHost,O.Safe;class r1 extends C{constructor(e){super(),_(this,e,o1,i1,w,{})}}function l1(s){let e,t,a=[{xmlns:"http://www.w3.org/2000/svg"},{width:"256"},{height:"227"},{"data-ds-icon":"fa"},{preserveAspectRatio:"xMidYMid"},{viewBox:"0 0 256 227"},s[0]],i={};for(let n=0;n<a.length;n+=1)i=g(i,a[n]);return{c(){e=d("svg"),t=new D(!0),this.h()},l(n){e=R(n,"svg",{xmlns:!0,width:!0,height:!0,"data-ds-icon":!0,preserveAspectRatio:!0,viewBox:!0});var o=H(e);t=F(o,!0),o.forEach(u),this.h()},h(){t.a=null,f(e,i)},m(n,o){V(n,e,o),t.m('<path d="M148.368 12.403a23.935 23.935 0 0 0-41.003 0L73.64 70.165c52.426 26.174 87.05 78.177 90.975 136.642h-23.679c-3.918-50.113-34.061-94.41-79.238-116.448l-31.213 53.97a81.595 81.595 0 0 1 47.307 62.375h-54.38a3.895 3.895 0 0 1-3.178-5.69l15.069-25.626a55.046 55.046 0 0 0-17.221-9.738L3.167 191.277a23.269 23.269 0 0 0 8.662 31.982 23.884 23.884 0 0 0 11.583 3.075h74.471a99.432 99.432 0 0 0-41.003-88.72l11.84-20.5c35.679 24.504 55.754 66.038 52.79 109.22h63.094c2.99-65.43-29.047-127.512-84.107-162.986l23.935-41.002a3.947 3.947 0 0 1 5.382-1.384c2.716 1.486 103.993 178.208 105.89 180.258a3.895 3.895 0 0 1-3.486 5.792h-24.396c.307 6.526.307 13.035 0 19.528h24.499A23.528 23.528 0 0 0 256 202.91a23.015 23.015 0 0 0-3.178-11.685z"/>',e)},p(n,[o]){f(e,i=A(a,[{xmlns:"http://www.w3.org/2000/svg"},{width:"256"},{height:"227"},{"data-ds-icon":"fa"},{preserveAspectRatio:"xMidYMid"},{viewBox:"0 0 256 227"},1&o&&n[0]]))},i:c,o:c,d(n){n&&u(e)}}}function c1(s,e,t){return s.$$set=a=>{t(0,e=g(g({},e),y(a)))},[e=y(e)]}class h1 extends C{constructor(e){super(),_(this,e,c1,l1,w,{})}}function u1(s){switch(s){case $.STRIPE:return{displayName:"Stripe",icon:r1};case $.SENTRY:return{displayName:"Sentry",icon:h1};default:return}}function I1(s){const e=u1(s.name);return e&&(s.displayName=e.displayName,s.icon=e.icon),s}function z1(s){return s.filter(e=>e.name!==$.STRIPE&&e.name!==$.SENTRY)}function Z1(s){return s.filter(e=>e.name===$.STRIPE||e.name===$.SENTRY)}export{D1 as A,W as B,M1 as C,R1 as D,f1 as F,k1 as G,E1 as L,F1 as M,B1 as N,$ as P,S1 as R,N1 as S,P1 as T,A1 as U,V1 as a,q as b,I1 as c,$1 as d,z1 as e,Z1 as f,T1 as g,y1 as h,H1 as i,x1 as j,p1 as k,v1 as l,w1 as m,C1 as n,_1 as o,ye as p,L1 as q,ee as r,u1 as s};
